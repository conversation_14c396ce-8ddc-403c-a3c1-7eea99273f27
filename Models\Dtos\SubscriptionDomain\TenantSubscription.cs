using static System.Runtime.InteropServices.JavaScript.JSType;

namespace SuperAdmin.Service.Models.Dtos.SubscriptionDomain;

public class TenantSubscription
{
    public string CompanyName { get; set; }
    public string Country { get; set; }
    public string Email { get; set; }
    public string PlanName { get; set; }
    public int UsersInPlan { get; set; }
    public DateTime TransactionDate { get; set; }
    public double Amount { get; set; }
    public string PaymentProvider { get; set; }
    public string TenantId { get; set; }
}

public class TenentSubscriptionResponse
{
    public object user { get; set; }
    public Data Data { get; set; }
    public string ResponseCode { get; set; }
    public object ErrorCode { get; set; }
    public string ResponseMessage { get; set; }
    public object DevResponseMessage { get; set; }
    public object StackTrace { get; set; }
}

public class Data
{
    public int PageSize { get; set; }
    public int PageNumber { get; set; }
    public int TotalSize { get; set; }
    public List<TenentSubscriptionItem> Items { get; set; }
}

public class TenentSubscriptionItem
{
    public string CompanyName { get; set; }
    public string Country { get; set; }
    public string Email { get; set; }
    public string PlanName { get; set; }
    public int UsersInPlan { get; set; }
    public DateTime? TransactionDate { get; set; }
    public double Amount { get; set; }
    public string PaymentProvider { get; set; }
    public string TenantId { get; set; }
}


