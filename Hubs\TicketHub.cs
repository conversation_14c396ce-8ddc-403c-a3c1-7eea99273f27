using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace SuperAdmin.Service.Hubs;

[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme + "," + "monolith-schema")]
public class TicketHub : Hub
{
    private readonly Dictionary<string, (string Email, string RoleFromToken)> ConnectionIdInfoMap = new();

    public override Task OnConnectedAsync()
    {
        var (email, role) = GetEmailAndRoleFromClaims();
        var connectionId = Context?.ConnectionId;

        if (!string.IsNullOrEmpty(connectionId) && !string.IsNullOrEmpty(role))
        {
            if (IsSuperAdminOrAdmin(role))
                Groups.AddToGroupAsync(connectionId, "SuperAdmin");
            ConnectionIdInfoMap[connectionId] = (email, role);
        }
        return base.OnConnectedAsync();
    }

    public override Task OnDisconnectedAsync(Exception? exception)
    {
        var connectionId = Context?.ConnectionId;
        if (!string.IsNullOrEmpty(connectionId))
            ConnectionIdInfoMap.Remove(connectionId);
        return base.OnDisconnectedAsync(exception);
    }


    private static bool IsSuperAdminOrAdmin(string role) => role == "SuperAdmin" || role == "Admin";

    private (string Email, string Role) GetEmailAndRoleFromClaims()
    {
        var emailClaim = Context?.User?.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Email || c.Type == ClaimTypes.Email);
        var roleClaim = Context?.User?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role);

        var email = emailClaim?.Value ?? string.Empty;
        var role = roleClaim?.Value ?? "Admin";

        return (email, role);
    }

    public string GetConnectionIdByEmail(string email)
    {
        var connection = ConnectionIdInfoMap.FirstOrDefault(x => x.Value.Email == email);
        return connection.Key;
    }
}
