namespace SuperAdmin.Service.Models.Dtos.ContactDomain
{
    public class ContactDto
    {
        public Guid Id { get; set; }
        public string UserId { get; set; } = default!;
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string PhoneNumber { get; set; } = default!;
        public DateTime CreatedAt { get; set; }
        public DateTime ModifiedAt { get; set; }
        public string? Industry { get; set; }
        public string? Subdomain { get; set; }
    }
}
