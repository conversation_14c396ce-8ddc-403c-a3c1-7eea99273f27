using System.ComponentModel.DataAnnotations;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.ActivityDomain;

public class ActivityQueryParameters : Pagination
{
    [Required]
    public Applications Application { get; set; }

    public string? CompanyName { get; set; }

    [DateRangeValidation]
    public DateTime? FromDate { get; set; }
    [DateRangeValidation]
    public DateTime? ToDate { get; set; }
}