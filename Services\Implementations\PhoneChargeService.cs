﻿using Configurations.Utility;
using Google.Apis.Http;
using Grpc.Core;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Models.Dtos.AuthDomain;
using SuperAdmin.Service.Models.Dtos.RuleEngineDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Diagnostics.Metrics;
using System.Net;
using System.Net.Http.Headers;
using Twilio.TwiML.Messaging;

namespace SuperAdmin.Service.Services.Implementations
{
    public class PhoneChargeService : IPhoneChargeService
    {
        private readonly UserManager<User> _userManager; 
        private readonly AppDbContext _dbContext;
        private readonly IConfiguration _config;
        private readonly string _phoneServiceBaseUrl;
        private readonly string _createChargeEndpoint;
        private readonly string _updateChargeEndpoint;
        private readonly string _getChargeEndpoint;
        private readonly string _getPhoneAmountEndpoint;
        private readonly System.Net.Http.IHttpClientFactory _httpClientFactory;
        private readonly IOtpService _otpService;
        private readonly IActivityLogService _activityLogService; 

        public PhoneChargeService(UserManager<User> userManager, AppDbContext dbContext, IConfiguration config, System.Net.Http.IHttpClientFactory httpClientFactory,
            IOtpService otpService, IActivityLogService activityLogService)
        {
            _userManager = userManager;
            _dbContext = dbContext;
            _config = config;
            _phoneServiceBaseUrl = _config["Services:PhoneService"];
            _createChargeEndpoint = _config["PhoneServiceEndpoints:CreatePhoneChargeConfiguration"];
            _updateChargeEndpoint = _config["PhoneServiceEndpoints:UpdatePhoneChargeConfiguration"];
            _getChargeEndpoint = _config["PhoneServiceEndpoints:GetPhoneChargeConfiguration"];
            _getPhoneAmountEndpoint = _config["PhoneServiceEndpoints:GetPhoneChargeAmount"];
            _httpClientFactory = httpClientFactory;
            _otpService = otpService;
            _activityLogService = activityLogService;
        }

        public async Task<ApiResponse<object>> CreatePhoneChargeSettings(string userId, PhoneChargeDto model)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            // Todo: use grpc to communicate with phone service one Samuel is ready  

            var country = await _dbContext.Country.FirstOrDefaultAsync(x => x.Id == model.CountryId);
            if (country is null)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Invalid country id"
                };
            }

            PhoneServiceRequest createChargeReguest = new()
            {
                amount = model.ChargeValue,
                countryCode = country.TwoLetterCode,
                countryName = country.Name,
                requestBy = $"{user.FirstName} {user.LastName}"
            };
            string module = string.Empty;
            switch (model.ChargeType)
            {
                case PhoneChargeType.PhonePurchase:
                    module = PhoneChargeType.PhonePurchase.ToString();
                    createChargeReguest.percentageOption = PercentageOption.phonePurchaseAmount.ToString();
                    break;
                case PhoneChargeType.Call:
                    module = PhoneChargeType.Call.ToString();
                    createChargeReguest.percentageOption = PercentageOption.callPercentage.ToString();
                    break;
                case PhoneChargeType.SMS:
                    module = PhoneChargeType.SMS.ToString();
                    createChargeReguest.percentageOption = PercentageOption.smsPercentage.ToString();
                    break;
            }

            string serializedPayload = JsonConvert.SerializeObject(createChargeReguest);

            using HttpClient client = _httpClientFactory.CreateClient();

            var request = new HttpRequestMessage(HttpMethod.Post, $"{_phoneServiceBaseUrl}{_createChargeEndpoint}")
            {
                Content = new StringContent(serializedPayload)
            };
            request.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var rawResponse = await client.SendAsync(request);
            var jsonString = await rawResponse.Content.ReadAsStringAsync();
            var res = JsonConvert.DeserializeObject<PhoneServiceResponse>(jsonString);
            if (res?.statusCode == 406)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "409",
                    ResponseMessage = $"{model.ChargeType} value already exists for {country.Name}"
                };
            }
            else if (res?.statusCode == 0)
            {
                await _activityLogService.LogActivity(Database.Enums.ActivityActionType.Creation, "",
                $"{user.FirstName} created {module} charge", "", user.Id, model.Applications);
                
                return new ApiResponse<object>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"{res.message}"
                };
            }
            else
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"{res?.message}"
                };
            }
        }

        public async Task<ApiResponse<object>> UpdatePhoneChargeSettings(string userId, UpdatePhoneChargeDto model)
        {

            if (userId != model.UserId)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = "You cannot update this setting. Please login"
                };
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            bool isPinValid = BCrypt.Net.BCrypt.Verify(model.Pin, user.PinHash);
            if (!isPinValid)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Incorrect pin"
                };
            }

            (bool isOtpValid, string message) = await _otpService.ValidateOtpUsingPhoneNumber(model.UserId, user.PhoneNumber!, model.Otp, OtpType.PhoneService);
            if (!isOtpValid)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = message
                };
            }

            bool isPasswordValid = await _userManager.CheckPasswordAsync(user, model.Password);
            if (!isPasswordValid)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Incorrect password"
                };
            }           

            // Todo: use grpc to communicate with phone service one Samuel is ready   
           
            UpdatePhoneServiceRequest updateChargeReguest = new()
            {
                configId = model.ConfigId.ToString(),
                amount = model.ChargeValue,
               /// requestBy = "Ajibola"
                requestBy = $"{user.FirstName} {user.LastName}"
            };

            string module = string.Empty;

            switch (model.ChargeType)
            {
                case PhoneChargeType.PhonePurchase:
                    module = PhoneChargeType.PhonePurchase.ToString();
                    updateChargeReguest.percentageOption = PercentageOption.phonePurchaseAmount.ToString();
                    break;
                case PhoneChargeType.Call:
                    module = PhoneChargeType.Call.ToString();
                    updateChargeReguest.percentageOption = PercentageOption.callPercentage.ToString();
                    break;
                case PhoneChargeType.SMS:
                    module = PhoneChargeType.SMS.ToString();
                    updateChargeReguest.percentageOption = PercentageOption.smsPercentage.ToString();
                    break;
            }

            string serializedPayload = JsonConvert.SerializeObject(updateChargeReguest);

            using HttpClient client = _httpClientFactory.CreateClient();

            var request = new HttpRequestMessage(HttpMethod.Put, $"{_phoneServiceBaseUrl}{_updateChargeEndpoint}")
            {
                Content = new StringContent(serializedPayload)
            };
            request.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var rawResponse = await client.SendAsync(request);
            var jsonString = await rawResponse.Content.ReadAsStringAsync();
            var res = JsonConvert.DeserializeObject<PhoneServiceResponse>(jsonString);
            if (res?.statusCode == 406)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "409",
                    //ResponseMessage = $"{model.ChargeType} value already exists for {country.Name}"
                    ResponseMessage = $"{res?.message}"
                };
            }
            else if (res?.statusCode == 0)
            {
                await _activityLogService.LogActivity(Database.Enums.ActivityActionType.Modification, "",
                $"{user.FirstName} updated {module} charge", "", user.Id, model.Applications);

                return new ApiResponse<object>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"{res?.message}"
                };
            }
            else
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"{res?.message}"
                };
            }
        }

        public async Task<ApiResponse<PhoneServiceGetResponse>> GetPhoneChargeSettings(string userId, PhoneChargeType chargeType, int pageIndex, Applications? applications)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<PhoneServiceGetResponse>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            // Todo: use grpc to communicate with phone service one Samuel is ready  
            
            GetPhoneChargeConfRequest getChargeReguest = new()
            {
                pageIndex = pageIndex,
            };

            string module = string.Empty;
            switch (chargeType)
            {
                case PhoneChargeType.PhonePurchase:
                    module = PhoneChargeType.PhonePurchase.ToString();
                    getChargeReguest.percentageOption = PercentageOption.phonePurchaseAmount.ToString();
                    break;
                case PhoneChargeType.Call:
                    module = PhoneChargeType.Call.ToString();
                    getChargeReguest.percentageOption = PercentageOption.callPercentage.ToString();
                    break;
                case PhoneChargeType.SMS:
                    module = PhoneChargeType.SMS.ToString();
                    getChargeReguest.percentageOption = PercentageOption.smsPercentage.ToString();
                    break;
            }

            string serializedPayload = JsonConvert.SerializeObject(getChargeReguest);

            using HttpClient client = _httpClientFactory.CreateClient();

            var request = new HttpRequestMessage(HttpMethod.Post, $"{_phoneServiceBaseUrl}{_getChargeEndpoint}")
            {
                Content = new StringContent(serializedPayload)
            };
            request.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var rawResponse = await client.SendAsync(request);
            var jsonString = await rawResponse.Content.ReadAsStringAsync();
            var res = JsonConvert.DeserializeObject<PhoneServiceGetResponse>(jsonString);

            if (rawResponse.StatusCode == HttpStatusCode.Created)
            {
                await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
                $"{user.FirstName} Viewed {module} charge", "", user.Id, applications);

                return new ApiResponse<PhoneServiceGetResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "successfull",
                    Data = res
                };
            }
            else
            {
                return new ApiResponse<PhoneServiceGetResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed"
                };
            }
        }

        public async Task<ApiResponse<GetPhoneAmount>> GetPhoneNumberAmount(string userId, string countryId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<GetPhoneAmount>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            // Todo: use grpc to communicate with phone service one Samuel is ready 

            var country = await _dbContext.Country.FirstOrDefaultAsync(x => x.Id == Guid.Parse(countryId));
            if (country is null)
            {
                return new ApiResponse<GetPhoneAmount>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Invalid country id"
                };
            }


            using HttpClient client = _httpClientFactory.CreateClient();

            string requestUrl = $"{_phoneServiceBaseUrl}{_getPhoneAmountEndpoint}/{country.TwoLetterCode}";

            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, requestUrl);

            HttpResponseMessage rawResponse = await client.SendAsync(request);
            var jsonString = await rawResponse.Content.ReadAsStringAsync();


            var res = JsonConvert.DeserializeObject<GetPhoneAmount>(jsonString);

            if (rawResponse.StatusCode == HttpStatusCode.OK)
            {                
                return new ApiResponse<GetPhoneAmount>
                {
                    ResponseCode = "200",
                    ResponseMessage = "successfull",
                    Data = res
                };
            }
            else
            {
                return new ApiResponse<GetPhoneAmount>
                {
                    ResponseCode = "404",
                    ResponseMessage = "No available phone number provision for this country"
                };
            }
        }
    }
}
