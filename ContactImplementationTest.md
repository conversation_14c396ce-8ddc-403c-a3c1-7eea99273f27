# Contact Upload Implementation Test Guide

## Overview
This document provides a test guide for the contact upload implementation that has been created.

## Implementation Summary

### Features Implemented:
1. **Contact Entity**: Database entity with UserId, Name, Email (optional), PhoneNumber
2. **Contact Upload**: Single contact and bulk contact upload
3. **Contact Management**: CRUD operations for contacts
4. **Upload Status Check**: Endpoint to check if user has uploaded contacts
5. **Validation**: Phone number uniqueness per user, optional email validation

### API Endpoints Created:

#### 1. Create Single Contact
- **POST** `/api/v1/contact`
- **Body**: 
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",  // Optional
  "phoneNumber": "+1234567890"
}
```

#### 2. Upload Multiple Contacts
- **POST** `/api/v1/contact/upload`
- **Body**: 
```json
[
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",  // Optional
    "phoneNumber": "+1234567890"
  },
  {
    "name": "<PERSON>",
    "phoneNumber": "+1987654321"
  }
]
```

#### 3. Get User Contacts
- **GET** `/api/v1/contact`
- Returns list of user's contacts

#### 4. Check Upload Status
- **GET** `/api/v1/contact/has-uploaded`
- Returns boolean indicating if user has uploaded contacts

#### 5. Update Contact
- **PUT** `/api/v1/contact/{contactId}`
- **Body**: Same as create contact

#### 6. Delete Contact
- **DELETE** `/api/v1/contact/{contactId}`

### Key Features:
- **Authentication Required**: All endpoints require JWT authentication
- **User Isolation**: Users can only access their own contacts
- **Phone Number Uniqueness**: Each user can only have one contact per phone number
- **Optional Email**: Email is not required but if provided, must be unique per user
- **Soft Delete**: Contacts are marked as deleted, not physically removed
- **Validation**: Proper validation for email format and phone number format
- **Error Handling**: Comprehensive error handling with meaningful messages

### Database Migration Required:
A new migration needs to be created and applied to add the Contact table to the database.

## Testing Steps:

1. **Build the project** to ensure no compilation errors
2. **Run database migration** to create Contact table
3. **Test authentication** - ensure endpoints require valid JWT token
4. **Test single contact creation** with and without email
5. **Test bulk upload** with mixed valid/invalid data
6. **Test duplicate prevention** - phone numbers and emails
7. **Test upload status check** before and after uploading contacts
8. **Test CRUD operations** - get, update, delete contacts

## Files Created/Modified:

### New Files:
- `Database/Entities/Contact.cs`
- `Models/Dtos/ContactDomain/ContactDto.cs`
- `Models/Dtos/ContactDomain/CreateContactDto.cs`
- `Models/Dtos/ContactDomain/ContactUploadResponseDto.cs`
- `Services/Contracts/IContactService.cs`
- `Services/Implementations/ContactService.cs`
- `Controllers/ContactController.cs`

### Modified Files:
- `Database/AppDbContext.cs` - Added Contacts DbSet
- `Extensions/ServiceCollectionExtensions.cs` - Registered ContactService

## Next Steps:
1. Create and run database migration
2. Test the implementation
3. Add any additional validation or business rules as needed
