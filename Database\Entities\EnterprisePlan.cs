﻿using SuperAdmin.Service.Models.Dtos.WeavrDomain;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Net;

namespace SuperAdmin.Service.Database.Entities
{
    public class EnterprisePlan : BaseEntity
    {
        public EnterprisePlan() 
        {
            Id = Guid.NewGuid();
            CreatedAt = DateTime.UtcNow;
            ModifiedAt = DateTime.UtcNow;
        }

        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public string AdminUserId { get; set; }
        public string CountryId { get; set; }
        public int MaxNumberofUser { get; set; }
        public bool IsNumberOfUserUnlimited { get; set; }
        public int StorageInGigaByte { get; set; }
        public bool IsStorageUnlimited { get; set; }
        public int CommunicationHistoryInMonth { get; set; }
        public bool KeepCommunicationForever { get; set; }
        public int ProjectCreation { get; set; }
        public bool KeepProjectCreationForever { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        public bool KeepDataRetentionForever { get; set; }
        public bool AIAssistance { get; set; }
        public bool UnlimitedActivityLog { get; set; }


        
        public virtual Company Company { get; set; }
        //Admin email address
        //Admin first name
        //Admin last name      
        //Enter Admin phone number

    }
}
