using System.Net;
using System.Text.Json;
using Configurations.Utility;
using Grpc.Core;
using Jobid;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Serilog;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Dtos.EnterpriseDomain;
using SuperAdmin.Service.Models.Dtos.RuleEngineDomain;
using SuperAdmin.Service.Models.Dtos.SubscriptionDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using ILogger = Serilog.ILogger;

namespace SuperAdmin.Service.Services.Implementations
{
    public class HttpSubscriptionService : ISubscriptionService, IDisposable
    {
        private readonly ILogger _logger = Log.ForContext<HttpSubscriptionService>();
        private readonly HttpClient _httpClient;
        private readonly ITicketCategoryService _ticketCategoryService;
        private readonly ITicketService _ticketService;
        private readonly AppDbContext _dbContext;
        private readonly IActivityLogService _activityLogService;
        private readonly UserManager<User> _userManager; 

        public HttpSubscriptionService(IConfiguration configuration, ITicketCategoryService ticketCategoryService,
            ITicketService ticketService, AppDbContext dbContext, IActivityLogService activityLogService, UserManager<User> userManager)
        {
            var baseAddress = configuration.GetSection("Services").GetSection("Monolith").Value;
            if (baseAddress == null)
                throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

            _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };
            _ticketCategoryService = ticketCategoryService;
            _ticketService = ticketService;
            _dbContext = dbContext;
            _activityLogService = activityLogService;
            _userManager = userManager;
        }


        public async Task<ApiResponse<Page<SubscriptionCompanyDetail>>> GetSubscribedCompanyDetail(FilterSubscriptionCompany request, string? subdomain, string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user is null)
                {
                    return new ApiResponse<Page<SubscriptionCompanyDetail>>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "User not found"
                    };
                }
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/all-company?" +
                                 $"application={request.Application}&fromDate={request.FromDate}&" +
                                 $"pageNumber={request.Page}&pageSize={request.PageSize}&" +
                                 $"periodFilter={request.PeriodFilter}&planId={request.PlanId}&" +
                                 $"sortBy={request.SortBy}&toDate={request.ToDate}&" +
                                 $"provider={request.Provider}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<Page<SubscriptionCompanyDetail>>>(responseBody); 

                    if(result?.Data?.Items != null)
                    {
                        foreach (var item in result.Data.Items)
                        {
                            if (item.CompanyName.Contains("-"))
                            {
                                var company = await _dbContext.Country.FirstOrDefaultAsync(x => x.Id == Guid.Parse(item.CompanyName));
                                if(company != null)
                                {
                                    item.CompanyName = company.Name;
                                }
                                else
                                {
                                    item.CompanyName = string.Empty;
                                }
                            }
                        }
                    }
                    await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
                $"{user.FirstName} Viewed Subscribed Company Detail ", "", user.Id, request.Application);
                    return new ApiResponse<Page<SubscriptionCompanyDetail>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<Page<SubscriptionCompanyDetail>>>(errorMessage)
                                           ?? new ApiResponse<Page<SubscriptionCompanyDetail>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetSubscribedCompanyDetail: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetSubscribedCompanyDetail: {ex}");
                return new ApiResponse<Page<SubscriptionCompanyDetail>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<ApplicationStatistic>> GetSummaryStatistics(Applications application, PaymentProviders? provider, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/statistic?" +
                                 $"application={application}&" +
                                 $"paymentProvider={provider}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<SummaryStatisticsResponse>>(responseBody);

                    var id = await _ticketCategoryService.GetCategoryByApplication(application);
                    int count = await _ticketService.TicketCountByCategory(id.ToString(), TicketStatus.PENDING);

                    return new ApiResponse<ApplicationStatistic>
                    {
                        Data = new ApplicationStatistic
                        {
                            TotalActive = result?.Data?.TotalActive,
                            TotalCompanies = result?.Data?.TotalCompanies,
                            PendingRequest = count,
                            TotalRevenue = result?.Data?.TotalRevenue,
                            TotalSubscriptions = result?.Data?.TotalSubscriptions
                        },
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    return JsonSerializer.Deserialize<ApiResponse<ApplicationStatistic>>(errorMessage)
                           ?? new ApiResponse<ApplicationStatistic>
                           {
                               ResponseCode = statusCode.ToString(),
                               ResponseMessage = "Failed to fetch data"
                           };
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetSummaryStatistics: {ex}");
                return new ApiResponse<ApplicationStatistic>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<List<ApplicationStatistic>>> GetAllApplicationSummaryStatistics(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/allstatistic?" +
                                 $"paymentProvider={string.Empty}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    List<ApplicationStatistic> resList = new();
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApplicationSummary>(responseBody);
                    //var result = JsonSerializer.Deserialize<ApiResponse<SummaryStatisticsResponse>>(responseBody);

                    if (result?.Data != null)
                    {
                        var jb = await _ticketCategoryService.GetCategoryByApplication(Applications.Joble);
                        var job = await _ticketCategoryService.GetCategoryByApplication(Applications.JobID);
                        var jobPays = await _ticketCategoryService.GetCategoryByApplication(Applications.JobPays);
                        var echo = await _ticketCategoryService.GetCategoryByApplication(Applications.Echo);

                        ApplicationStatistic jbApplication = new()
                        {
                            ApplicationName = Applications.Joble.ToString(),
                            TotalActive = result?.Data?.Joble.TotalActive,
                            TotalCompanies = result?.Data?.Joble.TotalCompanies,
                            PendingRequest = await _ticketService.TicketCountByCategory(jb.ToString(), TicketStatus.PENDING),
                            TotalRevenue = result?.Data?.Joble.TotalRevenue,
                            TotalSubscriptions = result?.Data?.Joble.TotalSubscriptions
                        };
                        ApplicationStatistic jobIdApplication = new()
                        {
                            ApplicationName = Applications.JobID.ToString(),
                            TotalActive = result?.Data?.JobID.TotalActive,
                            TotalCompanies = result?.Data?.JobID.TotalCompanies,
                            PendingRequest = await _ticketService.TicketCountByCategory(job.ToString(), TicketStatus.PENDING),
                            TotalRevenue = result?.Data?.JobID.TotalRevenue,
                            TotalSubscriptions = result?.Data?.JobID.TotalSubscriptions
                        };
                        ApplicationStatistic jobPaysApplication = new()
                        {
                            ApplicationName = Applications.JobPays.ToString(),
                            TotalActive = result?.Data?.JobPays.TotalActive,
                            TotalCompanies = result?.Data?.JobPays.TotalCompanies,
                            PendingRequest = await _ticketService.TicketCountByCategory(jobPays.ToString(), TicketStatus.PENDING),
                            TotalRevenue = result?.Data?.JobPays.TotalRevenue,
                            TotalSubscriptions = result?.Data?.JobPays.TotalSubscriptions
                        };

                        ApplicationStatistic echoApplication = new()
                        {
                            ApplicationName = Applications.Echo.ToString(),
                            TotalActive = result?.Data?.Echo.TotalActive,
                            TotalCompanies = result?.Data?.Echo.TotalCompanies,
                            PendingRequest = await _ticketService.TicketCountByCategory(echo.ToString(), TicketStatus.PENDING),
                            TotalRevenue = result?.Data?.Echo.TotalRevenue,
                            TotalSubscriptions = result?.Data?.Echo.TotalSubscriptions
                        };


                        resList.Add(jbApplication);
                        resList.Add(jobIdApplication);
                        resList.Add(jobPaysApplication);
                        resList.Add(echoApplication);

                        return new ApiResponse<List<ApplicationStatistic>>
                        {
                            Data = resList,
                            ResponseCode = result?.ResponseCode,
                            ResponseMessage = result?.ResponseMessage
                        };
                    }

                    //var id = await _ticketCategoryService.GetCategoryByApplication(application);
                    //int count = await _ticketService.TicketCountByCategory(id.ToString(), TicketStatus.PENDING);

                    return new ApiResponse<List<ApplicationStatistic>>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "No data"
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    return JsonSerializer.Deserialize<ApiResponse<List<ApplicationStatistic>>>(errorMessage)
                           ?? new ApiResponse<List<ApplicationStatistic>>
                           {
                               ResponseCode = statusCode.ToString(),
                               ResponseMessage = "Failed to fetch data"
                           };
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetSummaryStatistics: {ex}");
                return new ApiResponse<List<ApplicationStatistic>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }


        public async Task<ApiResponse<List<TotalSubscriptionCountPerPlan>>> GetTotalSubscriptionsCount(FilterSubscription request, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/total-count?" +
                                 $"application={request.Application}&fromDate={request.FromDate}&" +
                                 $"periodFilter={request.PeriodFilter}&toDate={request.ToDate}&" +
                                 $"paymentProvider={request.Provider}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<List<TotalSubscriptionCountPerPlan>>>(responseBody);

                    return new ApiResponse<List<TotalSubscriptionCountPerPlan>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<List<TotalSubscriptionCountPerPlan>>>(errorMessage)
                                           ?? new ApiResponse<List<TotalSubscriptionCountPerPlan>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetTotalSubscriptionsCount: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetTotalSubscriptionsCount: {ex}");
                return new ApiResponse<List<TotalSubscriptionCountPerPlan>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<PercentageIncrementPerPlanData>> GetPercentageIncrementPerPlan(FilterSubscription request, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/percentage-increment?" +
                                 $"application={request.Application}&fromDate={request.FromDate}&" +
                                 $"periodFilter={request.PeriodFilter}&toDate={request.ToDate}&" +
                                 $"paymentProvider={request.Provider}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<List<PercentageIncrementPerPlanDto>>>(responseBody);

                    double totalRevenue = 0;
                    int totalCount = 0;
                    // testing
                    if (result?.Data != null)
                    {
                        totalRevenue = result.Data.Sum(d => d.Revenue);
                        totalCount = result.Data.Sum(d => d.SubscriptionCount);
                    }

                    var data = new PercentageIncrementPerPlanData
                    {
                        Plans = result?.Data ?? new List<PercentageIncrementPerPlanDto>(),
                        TotalRevenue = totalRevenue,
                        TotalCount = totalCount
                    };

                    return new ApiResponse<PercentageIncrementPerPlanData>
                    {
                        Data = data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<PercentageIncrementPerPlanData>>(errorMessage)
                                           ?? new ApiResponse<PercentageIncrementPerPlanData>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetPercentageIncrementPerPlan: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetPercentageIncrementPerPlan: {ex}");
                return new ApiResponse<PercentageIncrementPerPlanData>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<List<TenentSubscriptionItem>>> GetTenantsSubscriptionHistory(FilterSubscriptionCompany request, string? subdomain, string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user is null)
                {
                    return new ApiResponse<List<TenentSubscriptionItem>>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "User not found"
                    };
                }
               
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/tenants/history?" +
                                 $"application={request.Application}&fromDate={request.FromDate}&" +
                                 $"pageNumber={request.Page}&pageSize={request.PageSize}&" +
                                 $"periodFilter={request.PeriodFilter}&planId={request.PlanId}&" +
                                 $"sortBy={request.SortBy}&toDate={request.ToDate}&" +
                                 $"provider={request.Provider}";

                var response = await _httpClient.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    
                    var result = JsonSerializer.Deserialize<TenentSubscriptionResponse>(responseBody);

                    await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
                     $"{user.FirstName} Viewed Tenants Subscription History ", "", user.Id, request.Application);

                    return new ApiResponse<List<TenentSubscriptionItem>>
                    {
                        Data = result.Data.Items,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    _logger.Error($"Error in GetTenantsSubscriptionHistory: {errorMessage}");

                    var errorRes = JsonSerializer.Deserialize<CreateDto>(responseBody);
                    return new ApiResponse<List<TenentSubscriptionItem>>
                    {
                        ResponseCode = statusCode.ToString(),
                        ResponseMessage = errorRes?.ResponseMessage
                    };
                                           
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetTenantsSubscriptionHistory: {ex}");
                return new ApiResponse<List<TenentSubscriptionItem>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<Page<TenantSubscriptionDetail>>> GetTenantSubscriptionHistory(string tenantId, FilterSubscriptionCompany request, string? subdomain, string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user is null)
                {
                    return new ApiResponse<Page<TenantSubscriptionDetail>>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "User not found"
                    };
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/tenant/history?" +
                                 $"tenantId={tenantId}&" +
                                 $"application={request.Application}&fromDate={request.FromDate}&" +
                                 $"pageNumber={request.Page}&pageSize={request.PageSize}&" +
                                 $"periodFilter={request.PeriodFilter}&planId={request.PlanId}&" +
                                 $"sortBy={request.SortBy}&toDate={request.ToDate}&" +
                                 $"provider={request.Provider}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<Page<TenantSubscriptionDetail>>>(responseBody);

                    await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
                    $"{user.FirstName} Viewed Tenant Subscription History ", "", user.Id, request.Application);

                    return new ApiResponse<Page<TenantSubscriptionDetail>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<Page<TenantSubscriptionDetail>>>(errorMessage)
                                           ?? new ApiResponse<Page<TenantSubscriptionDetail>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetTenantSubscriptionHistory: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetTenantSubscriptionHistory: {ex}");
                return new ApiResponse<Page<TenantSubscriptionDetail>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<List<ProviderRevenue>>> GetProviderRevenue(Applications application, TimePeriodFilter periodFilter, DateTime? fromDate, DateTime? toDate, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/revenue?" +
                                 $"application={application}&fromDate={fromDate}&" +
                                 $"periodFilter={periodFilter}&toDate={toDate}&";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<List<ProviderRevenue>>>(responseBody);

                    return new ApiResponse<List<ProviderRevenue>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<List<ProviderRevenue>>>(errorMessage)
                                           ?? new ApiResponse<List<ProviderRevenue>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetProviderRevenue: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetProviderRevenue: {ex}");
                return new ApiResponse<List<ProviderRevenue>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }

        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing) => _httpClient.Dispose();
    }
}
