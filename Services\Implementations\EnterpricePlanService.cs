﻿using Configurations.Utility;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Models.Dtos.EnterpriseDomain;
using SuperAdmin.Service.Services.Contracts;
using System.Net;
using System.Net.Http.Headers;
using System.Text;

namespace SuperAdmin.Service.Services.Implementations
{
    public class EnterpricePlanService : IEnterpricePlanService
    {
        private readonly AppDbContext _dbContext;
        private readonly UserManager<User> _userManager;
        private readonly IUserService _userService;
        private readonly IConfiguration _config;
        private readonly string _jobIdBaseUrl;
        private readonly string _getSubscriptionPlan;
        private readonly string _createEnterpricePlan;
        private readonly string _editEnterpricePlan;
        private readonly string _createEnterpricePlanForExistingCompany;
        private readonly string _deleteEnterpricePlan;
        private readonly string _getEnterpricePlan;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly HttpClient _httpClient;

        public EnterpricePlanService(AppDbContext dbContext, UserManager<User> userManager, IUserService userService,
            IConfiguration config, IHttpClientFactory httpClientFactory)
        {
            _dbContext = dbContext;
            _userManager = userManager;
            _userService = userService;
            _config = config;
            _jobIdBaseUrl = _config["Services:Monolith"];
            _getSubscriptionPlan = _config["JobIdEndpoints:GetSubscriptionPlan"];
            _createEnterpricePlan = _config["JobIdEndpoints:CreateEnterPrisePlan"];
            _createEnterpricePlanForExistingCompany = _config["JobIdEndpoints:CreateEnterPrisePlanForExistingCompany"];
            _getEnterpricePlan = _config["JobIdEndpoints:GetEnterpricePlan"];
            _editEnterpricePlan = _config["JobIdEndpoints:EditEnterPrisePlan"];
            _deleteEnterpricePlan = _config["JobIdEndpoints:DeleteEnterPrisePlan"];
            _httpClientFactory = httpClientFactory;
            var baseAddress = _config.GetSection("Services")?.GetSection("Monolith")?.Value;
            if (baseAddress == null)
                throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

            _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };

        }

        public async Task<ApiResponse<object>> CreateEnterpricePlan(EnterpriseDto model, string userId, string subdomain, string accessToken)
        {
            //var user = await _userManager.FindByIdAsync(userId);
            //if (user is null)
            //{
            //    return new ApiResponse<object>
            //    {
            //        ResponseCode = "404",
            //        ResponseMessage = "User not found"
            //    };
            //}

            if (!model.IsNumberOfUserUnlimited && model.MaxNumberofUser == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid maximum number of users or enable unlimited users."
                };
            }

            if (!model.IsStorageUnlimited && model.StorageInGigaByte == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid storage capacity or enable unlimited storage."
                };
            }


            if (!model.KeepCommunicationForever && model.CommunicationHistoryInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid duration for communication history or ensure it's kept forever."
                };
            }

            if (!model.KeepProjectCreationForever && model.ProjectCreation == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid number of projects that can be created or ensure project creation is kept forever."
                };
            }

            if (!model.KeepDataRetentionForever && model.DataRetentionPeriodInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid data retention period or ensure data retention is kept forever."
                };
            }

            // calling service to create enterprise plan

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
            _httpClient.Timeout = Timeout.InfiniteTimeSpan;
            var rawResponse = await _httpClient.GetAsync($"{_getSubscriptionPlan}?application={model.Application}");

            if (rawResponse.StatusCode == HttpStatusCode.OK)
            {
                var jsonString = await rawResponse.Content.ReadAsStringAsync();
                var res = JsonConvert.DeserializeObject<GetPlanDto>(jsonString);

                string planId = "";
                foreach (var item in res.Data)
                {
                    if (item.Name == "Enterprise")
                    {
                        planId = item.Id;
                        break;
                    }
                }

                MultipartFormDataContent formData = new MultipartFormDataContent();

                // Add form fields to the FormDataContent
                formData.Add(new StringContent(model.Application.ToString()), "EnterPriseOptions.Application");
                formData.Add(new StringContent(model.Provider.ToString()), "EnterPriseOptions.Provider");
                formData.Add(new StringContent(planId), "EnterPriseOptions.PlanId");
                formData.Add(new StringContent(""), "EnterPriseOptions.SubscriptionId");

                int projectLimit = model.KeepProjectCreationForever == true ? 0 : model.ProjectCreation;
                formData.Add(new StringContent(projectLimit.ToString()), "EnterPriseOptions.ProjectLimit");

                int communication = model.KeepCommunicationForever == true ? 0 : model.CommunicationHistoryInMonth;
                formData.Add(new StringContent(communication.ToString()), "EnterPriseOptions.InternalCommunicationHistoryLimit");

                int dataRetention = model.KeepDataRetentionForever == true ? 0 : model.DataRetentionPeriodInMonth;
                formData.Add(new StringContent(dataRetention.ToString()), "EnterPriseOptions.DataRetentionPeriodInMonth");

                formData.Add(new StringContent(model.UnlimitedActivityLog.ToString()), "EnterPriseOptions.ActivityLogHistoryLimit");

                formData.Add(new StringContent(model.CalenderLimit.ToString()), "EnterPriseOptions.CalenderLimit");

                formData.Add(new StringContent(model.TimeSheetManagement.ToString()), "EnterPriseOptions.TimeSheetManagement");

                int storage = model.IsStorageUnlimited == true ? 0 : model.StorageInGigaByte;
                formData.Add(new StringContent(storage.ToString()), "EnterPriseOptions.StorageLimit");

                int userLimit = model.IsNumberOfUserUnlimited == true ? 0 : model.MaxNumberofUser;
                formData.Add(new StringContent(userLimit.ToString()), "EnterPriseOptions.UsersLimit");

                formData.Add(new StringContent(model.AIAssistance.ToString()), "EnterPriseOptions.AiAssistants");
                formData.Add(new StringContent(model.AmountPaid.ToString()), "EnterPriseOptions.AmountPaid");

                formData.Add(new StringContent(model.TransactionDate?.ToString("yyyy-MM-ddTHH:mm:ss")), "EnterPriseOptions.TransactionDate");
                //formData.Add(new StringContent(model.ActivateOn?.ToString("yyyy-MM-ddTHH:mm:ss")), "ActivatedOn");
                //formData.Add(new StringContent(""), "ExpiresOn");
                //formData.Add(new StringContent(EnterPriseOptions.ExpiresOn?.ToString("yyyy-MM-ddTHH:mm:ss")), "ExpiresOn");
                formData.Add(new StringContent(model.Currency ?? ""), "EnterPriseOptions.Currency");
                formData.Add(new StringContent(model.TransactionCode ?? ""), "EnterPriseOptions.TransactionCode");
                //formData.Add(new StringContent(""), "TenantId");

                formData.Add(new StringContent(userId), "EnterPriseOptions.LoggedInUserId");
                formData.Add(new StringContent(model.PaymentId), "EnterPriseOptions.PaymentId");
                formData.Add(new StringContent(string.IsNullOrWhiteSpace(model.AdminFirstname) ? "" : model.AdminFirstname), "FirstName");
                formData.Add(new StringContent(model.AdminLastname ?? ""), "LastName");
                formData.Add(new StringContent(model.CompanyName), "CompanyName");
                formData.Add(new StringContent(model.AdminEmail), "Email");
                formData.Add(new StringContent(string.IsNullOrWhiteSpace(model.PersonalEmail) ? "" : model.PersonalEmail), "PersonalEmail");
                formData.Add(new StringContent(model.PhoneNumber), "PhoneNumber");
                formData.Add(new StringContent(model.Subdomain), "Domain");
                formData.Add(new StringContent(model.CompanyType), "CompanyType");
                formData.Add(new StringContent(model.Country), "Country");
                formData.Add(new StringContent(model.Industry.ToString()), "Industry");

                // Create a string to store the copied payload
                var copiedPayload = await CopyMultipartFormDataContentToString(formData);


                _httpClient.DefaultRequestHeaders.Clear();
                //_httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
                var rawResponsepost = await _httpClient.PostAsync($"{_createEnterpricePlan}", formData);
                var jsonStringg = await rawResponsepost.Content.ReadAsStringAsync();

                if (rawResponsepost.StatusCode == HttpStatusCode.OK)
                {
                    return new ApiResponse<object>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Enterprise plan created successful."
                    };
                }
                else
                {
                    var serialised = JsonConvert.DeserializeObject<CreateDto>(jsonStringg);
                    return new ApiResponse<object>
                    {
                        ResponseCode = "400",
                        ResponseMessage = serialised?.ResponseMessage
                    };
                }
            }
            else
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Unable to fetch subcription plan."
                };
            }
        }

        public async Task<ApiResponse<object>> CreateEnterpricePlanForExistingCompany(ExistingCompanyEnterprisePlanDto model, string userId, string subdomain)
        {
            if (!model.IsNumberOfUserUnlimited && model.MaxNumberofUser == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid maximum number of users or enable unlimited users."
                };
            }

            if (!model.IsStorageUnlimited && model.StorageInGigaByte == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid storage capacity or enable unlimited storage."
                };
            }


            if (!model.KeepCommunicationForever && model.CommunicationHistoryInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid duration for communication history or ensure it's kept forever."
                };
            }

            if (!model.KeepProjectCreationForever && model.ProjectCreation == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid number of projects that can be created or ensure project creation is kept forever."
                };
            }

            if (!model.KeepDataRetentionForever && model.DataRetentionPeriodInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid data retention period or ensure data retention is kept forever."
                };
            }


            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
            _httpClient.Timeout = Timeout.InfiniteTimeSpan;
            var rawResponse = await _httpClient.GetAsync($"{_getSubscriptionPlan}?application={model.Application}");

            if (rawResponse.StatusCode == HttpStatusCode.OK)
            {
                var jsonString = await rawResponse.Content.ReadAsStringAsync();
                var res = JsonConvert.DeserializeObject<GetPlanDto>(jsonString);

                string planId = "";
                foreach (var item in res.Data)
                {
                    if (item.Name == "Enterprise")
                    {
                        planId = item.Id;
                        break;
                    }
                }

                // calling service to edit enterprise plan
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
                //_httpClient.Timeout = Timeout.InfiniteTimeSpan;

                int projectLimit = model.KeepProjectCreationForever == true ? 0 : model.ProjectCreation;
                int communication = model.KeepCommunicationForever == true ? 0 : model.CommunicationHistoryInMonth;
                int storage = model.IsStorageUnlimited == true ? 0 : model.StorageInGigaByte;
                int userLimit = model.IsNumberOfUserUnlimited == true ? 0 : model.MaxNumberofUser;
                int dataRetention = model.KeepDataRetentionForever == true ? 0 : model.DataRetentionPeriodInMonth;
                int activityLogHistoryLimit = model.UnlimitedActivityLog == true ? 1 : 0;


                JObject obj = new JObject(
                    new JProperty("Application", model.Application),
                    new JProperty("Provider", model.Provider),
                    new JProperty("SubscriptionId", model.SubscriptionId),
                    new JProperty("PlanId", planId),
                    new JProperty("AmountPaid", model.AmountPaid),
                    new JProperty("TenantId", model.TenantId),
                    new JProperty("LoggedInUserId", userId),
                    new JProperty("ProjectLimit", projectLimit),
                    new JProperty("ActivityLogHistoryLimit", activityLogHistoryLimit),
                    new JProperty("CalenderLimit", model.CalenderLimit),
                    new JProperty("TimeSheetManagement", model.TimeSheetManagement),
                    new JProperty("StorageLimit", storage),
                    new JProperty("UsersLimit", userLimit),
                    new JProperty("AiAssistants", model.AIAssistance),
                    new JProperty("TransactionCode", model.TransactionCode),
                    new JProperty("TransactionDate", model.TransactionDate),
                    new JProperty("DataRetentionPeriodInMonth", dataRetention),
                    new JProperty("InternalCommunicationHistoryLimit", communication));

                StringContent _httpContent = new StringContent(obj.ToString());
                _httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                var rawResponsepost = await _httpClient.PostAsync($"{_createEnterpricePlanForExistingCompany}", _httpContent);
                var jsonStringg = await rawResponsepost.Content.ReadAsStringAsync();

                if (rawResponsepost.StatusCode == HttpStatusCode.OK)
                {
                    return new ApiResponse<object>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Enterprise plan created successfully."
                    };
                }
                else
                {
                    var serialised = JsonConvert.DeserializeObject<CreateDto>(jsonStringg);
                    return new ApiResponse<object>
                    {
                        ResponseCode = "400",
                        ResponseMessage = serialised?.DevResponseMessage
                    };
                }

            }
            else
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Unable to fetch subcription plan."
                };
            }
        }

        public async Task<ApiResponse<object>> EditEnterpricePlan(UpdateEnterprisePlanDto model, string userId, string subdomain)
        {
            if (!model.IsNumberOfUserUnlimited && model.MaxNumberofUser == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid maximum number of users or enable unlimited users."
                };
            }

            if (!model.IsStorageUnlimited && model.StorageInGigaByte == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid storage capacity or enable unlimited storage."
                };
            }


            if (!model.KeepCommunicationForever && model.CommunicationHistoryInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid duration for communication history or ensure it's kept forever."
                };
            }

            if (!model.KeepProjectCreationForever && model.ProjectCreation == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid number of projects that can be created or ensure project creation is kept forever."
                };
            }

            if (!model.KeepDataRetentionForever && model.DataRetentionPeriodInMonth == 0)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Please specify a valid data retention period or ensure data retention is kept forever."
                };
            }

            // calling service to edit enterprise plan
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
            _httpClient.Timeout = Timeout.InfiniteTimeSpan;

            int projectLimit = model.KeepProjectCreationForever == true ? 0 : model.ProjectCreation;
            int communication = model.KeepCommunicationForever == true ? 0 : model.CommunicationHistoryInMonth;
            int storage = model.IsStorageUnlimited == true ? 0 : model.StorageInGigaByte;
            int userLimit = model.IsNumberOfUserUnlimited == true ? 0 : model.MaxNumberofUser;
            int dataRetention = model.KeepDataRetentionForever == true ? 0 : model.DataRetentionPeriodInMonth;
            int activityLogHistoryLimit = model.UnlimitedActivityLog == true ? 1 : 0;


            JObject obj = new JObject(
                new JProperty("Application", model.Application),
                new JProperty("Provider", model.Provider),
                new JProperty("AmountPaid", model.AmountPaid),
                new JProperty("TenantId", model.TenantId),
                new JProperty("EnterPriseSubId", model.EnterPriseSubId),
                new JProperty("LoggedInUserId", userId),
                new JProperty("ProjectLimit", projectLimit),
                new JProperty("ActivityLogHistoryLimit", activityLogHistoryLimit),
                new JProperty("CalenderLimit", model.CalenderLimit),
                new JProperty("TimeSheetManagement", model.TimeSheetManagement),
                new JProperty("StorageLimit", storage),
                new JProperty("UsersLimit", userLimit),
                new JProperty("AiAssistants", model.AIAssistance),
                new JProperty("DataRetentionPeriodInMonth", dataRetention),
                new JProperty("InternalCommunicationHistoryLimit", communication));

            StringContent _httpContent = new StringContent(obj.ToString());
            _httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var rawResponsepost = await _httpClient.PutAsync($"{_editEnterpricePlan}", _httpContent);
            var jsonStringg = await rawResponsepost.Content.ReadAsStringAsync();

            if (rawResponsepost.StatusCode == HttpStatusCode.OK)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Enterprise plan edited successfully."
                };
            }
            else
            {
                var serialised = JsonConvert.DeserializeObject<CreateDto>(jsonStringg);
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = serialised?.DevResponseMessage
                };
            }

        }

        public async Task<ApiResponse<object>> DeleteEnterpricePlan(string tenantId, string userId, string enterPriseSubId, string subdomain)
        {
            // calling service to delete enterprise plan
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
            _httpClient.Timeout = Timeout.InfiniteTimeSpan;

            var url = $"{_deleteEnterpricePlan}/{tenantId}/{userId}/{enterPriseSubId}";
            var rawResponsepost = await _httpClient.DeleteAsync(url);
            var jsonStringg = await rawResponsepost.Content.ReadAsStringAsync();

            if (rawResponsepost.StatusCode == HttpStatusCode.OK)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Enterprise plan deleted successfully."
                };
            }
            else
            {
                var serialised = JsonConvert.DeserializeObject<CreateDto>(jsonStringg);
                return new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = serialised?.DevResponseMessage
                };
            }

        }

        static async Task<string> CopyMultipartFormDataContentToString(MultipartFormDataContent formData)
        {
            // Create a StringBuilder to build the string
            StringBuilder stringBuilder = new StringBuilder();

            // Iterate through the contents of the FormDataContent
            foreach (var content in formData)
            {
                // Check if it's a StringContent
                if (content is StringContent stringContent)
                {
                    // Read the string value asynchronously
                    string value = await stringContent.ReadAsStringAsync();

                    // Append the key-value pair to the string
                    stringBuilder.Append($"{content.Headers.ContentDisposition.Name}={value}&");
                }
                // You can add additional checks for other types of content if needed
            }

            // Remove the trailing '&' character
            string copiedPayload = stringBuilder.ToString().TrimEnd('&');

            return copiedPayload;
        }

        public async Task<ApiResponse<Page<EnterpriceData>>> GetEnterpricePlan(PaginationParameters parameters, string userId, string? subdomain,string?searchKeyword)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<Page<EnterpriceData>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

            var requestUri = _getEnterpricePlan + $"?PageNumber={parameters.PageNumber}&PageSize={parameters.PageSize}&searchKeyword={searchKeyword}";

            var response = await _httpClient.GetAsync(requestUri);
            var jsonString = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<ApiResponse<Page<EnterpriceData>>>(jsonString);

                return new ApiResponse<Page<EnterpriceData>>
                {
                    Data = result?.Data,
                    ResponseCode = result?.ResponseCode,
                    ResponseMessage = result?.ResponseMessage
                };
            }
            else
            {
                var statusCode = (int)response.StatusCode;
                var errorMessage = await response.Content.ReadAsStringAsync();

                var errorApiResponse = JsonConvert.DeserializeObject<ApiResponse<Page<EnterpriceData>>>(errorMessage)
                                       ?? new ApiResponse<Page<EnterpriceData>>
                                       {
                                           ResponseCode = statusCode.ToString(),
                                           ResponseMessage = "Failed to fetch data"
                                       };

                return errorApiResponse;
            }
        }
    }
}
