﻿using Configurations.Utility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.ProductUpdateDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Security.Claims;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/product-update")]    
    public class ProductUpdateController : BaseController
    {
        private readonly IProductUpdatesService _productUpdateService;

        public ProductUpdateController(IProductUpdatesService productUpdateService)
        {
            _productUpdateService = productUpdateService;
        }

        /// <summary>
        /// This endpoint gets all job suite packages
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("packages")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<string>>), 200)]
        public IActionResult GetPackages()
        {
            var response = _productUpdateService.GetPackages();
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint creates a new category for product updates
        /// </summary>
        /// <param name="categoryName"></param>
        /// <returns></returns>
        [HttpPost("categories/{categoryName}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> CreateProductUpdateCategory([FromRoute] string categoryName, Applications? applications)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.CreateProductUpdateCategory(categoryName,loggedInUser,applications);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all product update categories
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("categories")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<ProductUpdateCategoryDto>>), 200)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> GetProductUpdateCategories()
        {
            var response = await _productUpdateService.GetProductUpdateCategories();
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint creates a new product update to publish or save as draft
        /// </summary>
        /// <param name="publishCommand"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("{publishCommand}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> CreateProductUpdate([FromRoute] PublishCommand publishCommand, [FromForm] CreateProductUpdateDto model)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.CreateProductUpdate(publishCommand, model,loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all published product updates
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        [HttpGet("sent")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<SentProductUpdateDto>>), 200)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> GetSentProductUpdate([FromQuery] FilterProductUpdatesBy filterBy)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.GetSentProductUpdates(filterBy, loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all published product updates
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        [HttpGet("admin/sent")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<SentProductUpdateDto>>), 200)]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        public async Task<IActionResult> GetSentProductUpdateForAdmin([FromQuery] FilterProductUpdatesBy filterBy)
        {
            var response = await _productUpdateService.GetSentProductUpdatesForAdmin(filterBy);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all product updates saved as draft
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        [HttpGet("draft")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<DraftProductUpdateDto>>), 200)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> GetDraftProductUpdate([FromQuery] FilterProductUpdatesBy filterBy)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.GetDraftProductUpdates(filterBy, loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all deleted product updates
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        [HttpGet("deleted")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<DeletedProductUpdateDto>>), 200)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> GetDeletedProductUpdate([FromQuery] FilterProductUpdatesBy filterBy)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.GetDeletedProductUpdates(filterBy, loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint updates the draft product update
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPatch("draft/{id}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> UpdateDraftProductUpdate([FromRoute] Guid id, CreateProductUpdateDto model)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.UpdateDraftProductUpdates(id, model, loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint publishes draft product updates
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPatch("publish-draft/{id}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> SendDraftProductUpdate([FromRoute] Guid id, CreateProductUpdateDto model)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.SendDraftProductUpdates(id, model, loggedInUser);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint deletes product updates
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpDelete]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> DeleteProductUpdate([FromBody] DeleteProductUpdateDto model)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _productUpdateService.DeleteProductUpdates(model.Ids, loggedInUser, model.Applications);
            return ParseResponse(response);
        }

        [HttpDelete("admin")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        public async Task<IActionResult> DeleteProductUpdateForAdmin([FromBody] DeleteProductUpdateDto model)
        {
            var response = await _productUpdateService.DeleteProductUpdatesForAdmin(model.Ids);
            return ParseResponse(response);
        }
    }
}
