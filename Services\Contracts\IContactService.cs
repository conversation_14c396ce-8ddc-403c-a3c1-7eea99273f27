using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos.ContactDomain;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IContactService
    {
        Task<ApiResponse<ContactDto>> CreateContact(string userId, CreateContactDto model);
        Task<ApiResponse<ContactUploadResponseDto>> UploadContacts(string userId, List<CreateContactDto> contacts);
        Task<ApiResponse<List<ContactDto>>> GetUserContacts(string userId, string subdomain);
        Task<ApiResponse<bool>> HasUserUploadedContacts(string userId, string subdomain);
        Task<ApiResponse<ContactDto>> UpdateContact(string userId, Guid contactId, CreateContactDto model);
        Task<ApiResponse<dynamic>> DeleteContact(string userId, Guid contactId);
        Task<ApiResponse<List<ContactDto>>> GetAllContactsForSubdomain(string subdomain, int pageNumber, int pageSize);
        Task<ApiResponse<ContactDto>> GetContactById(Guid contactId);
    }
}
