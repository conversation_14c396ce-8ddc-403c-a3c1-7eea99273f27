﻿using Configurations.Utility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.PermissionDomain;
using SuperAdmin.Service.Models.Dtos.ProductUpdateDomain;
using SuperAdmin.Service.Models.Dtos.RoleDomain;
using SuperAdmin.Service.Services.Contracts;
using System.Security.Claims;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class RolesController : BaseController
    {
        private readonly IPermissionService _permissionService;
        private readonly IRoleService _roleService;

        public RolesController(
            IPermissionService permissionService,
            IRoleService roleService)
        {
            _permissionService = permissionService;
            _roleService = roleService;
        }

        /// <summary>
        /// This endpoint gets all permissions
        /// </summary>
        /// <returns></returns>
        [HttpGet("permissions")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<PermissionDto>>), 200)]
        public async Task<IActionResult> GetPermissions()
        {
            var response = await _permissionService.GetPermissions();
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets all roles
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<RoleDto>>), 200)]
        public async Task<IActionResult> GetRoles([FromQuery] FilterRoleBy model)
        {
            var response = await _roleService.GetRoles(model);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint creates a role mapped to permissions
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<CreateRole>), 400)]
        [ProducesResponseType(typeof(ApiResponse<CreateRole>), 200)]
        public async Task<IActionResult> CreateRole([FromBody] CreateRole model)
        {
            var response = await _roleService.CreateRole(model);
            return ParseResponse(response);
        }


        [HttpPut("edit-role")]
        [ProducesResponseType(typeof(ApiResponse<CreateRole>), 400)]
        [ProducesResponseType(typeof(ApiResponse<CreateRole>), 200)]
        public async Task<IActionResult> EditRole([FromBody] EditRoleDto model)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _roleService.EditRole(model);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint deletes a role
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 400)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 409)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        public async Task<IActionResult> DeleteRole([FromRoute] string id)
        {
            var response = await _roleService.DeleteRole(id);
            return ParseResponse(response);
        }

        /// <summary>
        /// This endpoint gets permissions for a role
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        [HttpGet("{roleId}/permissions")]
        [ProducesResponseType(typeof(ApiResponse<RolePermissionDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<RolePermissionDto>), 200)]
        public async Task<IActionResult> GetPermissionsForARole([FromRoute] string roleId)
        {
            var response = await _roleService.GetPermissionsForARole(roleId);
            return ParseResponse(response);
        }
    }
}
