using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace SuperAdmin.Service.Database.Entities
{
    /// <summary>
    /// Represents a currency rule that defines exchange rates and costs for different business lines and applications
    /// </summary>
    public class CurrencyRule : BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyRule"/> class
        /// </summary>
        public CurrencyRule()
        {
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Gets or sets the unique identifier for the currency rule
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the application this currency rule applies to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the business line this currency rule applies to
        /// </summary>
        public string BusinessLine { get; set; }

        /// <summary>
        /// Gets or sets the currency being sold/offered to customers
        /// </summary>
        public CurrencyCode SellCurrency { get; set; }

        /// <summary>
        /// Gets or sets the currency used for internal cost calculations
        /// </summary>
        public CurrencyCode CostCurrency { get; set; }

        /// <summary>
        /// Gets or sets the exchange rate for selling this currency to customers
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the internal cost amount for this currency conversion
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the type of external supplier used for this currency rule
        /// </summary>
        public ExternalSupplierType ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the cost charged by the external supplier for this currency conversion
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the date when this currency rule becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the current status of this currency rule (Active/Inactive)
        /// </summary>
        public CurrencyRuleStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who created this currency rule
        /// </summary>
        public string CreatedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who last modified this currency rule
        /// </summary>
        public string? ModifiedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the collection of versions for this currency rule
        /// </summary>
        public virtual ICollection<CurrencyRuleVersion> Versions { get; set; } = new List<CurrencyRuleVersion>();

        /// <summary>
        /// Gets or sets the user who created this currency rule
        /// </summary>
        public virtual User CreatedByUser { get; set; }

        /// <summary>
        /// Gets or sets the user who last modified this currency rule
        /// </summary>
        public virtual User? ModifiedByUser { get; set; }
    }
}
