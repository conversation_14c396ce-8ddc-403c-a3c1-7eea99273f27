using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Services.Contracts
{
    /// <summary>
    /// Service interface for managing currency rules and conversion rates
    /// </summary>
    public interface ICurrencyRuleService
    {
        #region Currency Rules CRUD Operations

        /// <summary>
        /// Retrieves a paginated list of currency rules based on the specified filters
        /// </summary>
        /// <param name="filter">The filter criteria to apply</param>
        /// <returns>A paginated response containing currency rules</returns>
        Task<ApiResponse<PaginationResult<CurrencyRuleDto>>> GetCurrencyRules(CurrencyRuleFilterDto filter);

        /// <summary>
        /// Retrieves a specific currency rule by its unique identifier
        /// </summary>
        /// <param name="id">The unique identifier of the currency rule</param>
        /// <returns>The currency rule if found, otherwise an error response</returns>
        Task<ApiResponse<CurrencyRuleDto>> GetCurrencyRuleById(Guid id);

        /// <summary>
        /// Creates a new currency rule with the specified properties
        /// </summary>
        /// <param name="model">The currency rule data to create</param>
        /// <param name="userId">The ID of the user creating the rule</param>
        /// <returns>The created currency rule</returns>
        Task<ApiResponse<CurrencyRuleDto>> CreateCurrencyRule(CreateCurrencyRuleDto model, string userId);

        /// <summary>
        /// Updates an existing currency rule with new values
        /// </summary>
        /// <param name="id">The unique identifier of the currency rule to update</param>
        /// <param name="model">The updated currency rule data</param>
        /// <param name="userId">The ID of the user updating the rule</param>
        /// <returns>The updated currency rule</returns>
        Task<ApiResponse<CurrencyRuleDto>> UpdateCurrencyRule(Guid id, UpdateCurrencyRuleDto model, string userId);

        /// <summary>
        /// Deletes a currency rule by marking it as deleted
        /// </summary>
        /// <param name="id">The unique identifier of the currency rule to delete</param>
        /// <param name="userId">The ID of the user deleting the rule</param>
        /// <returns>A boolean response indicating success or failure</returns>
        Task<ApiResponse<BooleanResponse>> DeleteCurrencyRule(Guid id, string userId);

        /// <summary>
        /// Activates or deactivates a currency rule
        /// </summary>
        /// <param name="id">The unique identifier of the currency rule</param>
        /// <param name="isActive">True to activate, false to deactivate</param>
        /// <param name="userId">The ID of the user performing the action</param>
        /// <returns>A boolean response indicating success or failure</returns>
        Task<ApiResponse<BooleanResponse>> ActivateDeactivateCurrencyRule(Guid id, bool isActive, string userId);

        #endregion

        #region Currency Rule Versions

        /// <summary>
        /// Retrieves a paginated list of versions for a specific currency rule
        /// </summary>
        /// <param name="currencyRuleId">The unique identifier of the currency rule</param>
        /// <param name="page">The page number to retrieve (default: 1)</param>
        /// <param name="pageSize">The number of items per page (default: 10)</param>
        /// <returns>A paginated response containing currency rule versions</returns>
        Task<ApiResponse<PaginationResult<CurrencyRuleVersionDto>>> GetCurrencyRuleVersions(Guid currencyRuleId, int page = 1, int pageSize = 10);

        /// <summary>
        /// Retrieves a specific currency rule version by its unique identifier
        /// </summary>
        /// <param name="versionId">The unique identifier of the currency rule version</param>
        /// <returns>The currency rule version if found, otherwise an error response</returns>
        Task<ApiResponse<CurrencyRuleVersionDto>> GetCurrencyRuleVersionById(Guid versionId);

        #endregion

        #region Currency Conversion Rates

        /// <summary>
        /// Retrieves a paginated list of currency conversion rates based on the specified filters
        /// </summary>
        /// <param name="filter">The filter criteria to apply</param>
        /// <returns>A paginated response containing currency conversion rates</returns>
        Task<ApiResponse<PaginationResult<CurrencyConversionRateDto>>> GetCurrencyConversionRates(CurrencyConversionRateFilterDto filter);

        /// <summary>
        /// Retrieves a specific currency conversion rate by its unique identifier
        /// </summary>
        /// <param name="id">The unique identifier of the currency conversion rate</param>
        /// <returns>The currency conversion rate if found, otherwise an error response</returns>
        Task<ApiResponse<CurrencyConversionRateDto>> GetCurrencyConversionRateById(Guid id);

        /// <summary>
        /// Creates a new currency conversion rate with the specified properties
        /// </summary>
        /// <param name="model">The currency conversion rate data to create</param>
        /// <param name="userId">The ID of the user creating the rate</param>
        /// <returns>The created currency conversion rate</returns>
        Task<ApiResponse<CurrencyConversionRateDto>> CreateCurrencyConversionRate(CreateCurrencyConversionRateDto model, string userId);

        /// <summary>
        /// Updates an existing currency conversion rate with new values
        /// </summary>
        /// <param name="id">The unique identifier of the currency conversion rate to update</param>
        /// <param name="model">The updated currency conversion rate data</param>
        /// <param name="userId">The ID of the user updating the rate</param>
        /// <returns>The updated currency conversion rate</returns>
        Task<ApiResponse<CurrencyConversionRateDto>> UpdateCurrencyConversionRate(Guid id, UpdateCurrencyConversionRateDto model, string userId);

        /// <summary>
        /// Deletes a currency conversion rate by marking it as inactive
        /// </summary>
        /// <param name="id">The unique identifier of the currency conversion rate to delete</param>
        /// <param name="userId">The ID of the user deleting the rate</param>
        /// <returns>A boolean response indicating success or failure</returns>
        Task<ApiResponse<BooleanResponse>> DeleteCurrencyConversionRate(Guid id, string userId);

        #endregion

        #region Bulk Import Operations

        /// <summary>
        /// Imports multiple currency rules from a bulk import request
        /// </summary>
        /// <param name="model">The bulk import data containing currency rules</param>
        /// <param name="userId">The ID of the user performing the import</param>
        /// <returns>A result indicating the success/failure of the bulk import operation</returns>
        Task<ApiResponse<BulkImportResultDto>> BulkImportCurrencyRules(BulkCurrencyRuleImportDto model, string userId);

        /// <summary>
        /// Imports multiple currency conversion rates from a bulk import request
        /// </summary>
        /// <param name="model">The bulk import data containing currency conversion rates</param>
        /// <param name="userId">The ID of the user performing the import</param>
        /// <returns>A result indicating the success/failure of the bulk import operation</returns>
        Task<ApiResponse<BulkImportResultDto>> BulkImportCurrencyConversionRates(BulkCurrencyRateImportDto model, string userId);

        #endregion

        #region Audit Trail

        /// <summary>
        /// Retrieves a paginated list of audit log entries for currency rule changes
        /// </summary>
        /// <param name="filter">The filter criteria to apply</param>
        /// <returns>A paginated response containing audit log entries</returns>
        Task<ApiResponse<PaginationResult<CurrencyRuleAuditLogDto>>> GetCurrencyRuleAuditLogs(CurrencyRuleAuditFilterDto filter);

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the current conversion rate between two currencies
        /// </summary>
        /// <param name="fromCurrency">The source currency code</param>
        /// <param name="toCurrency">The target currency code</param>
        /// <param name="effectiveDate">Optional effective date for the rate (defaults to current date)</param>
        /// <param name="application">Optional application filter</param>
        /// <returns>The conversion rate if found, otherwise an error response</returns>
        Task<ApiResponse<DecimalResponse>> GetConversionRate(string fromCurrency, string toCurrency, DateTime? effectiveDate = null, Applications? application = null);

        /// <summary>
        /// Retrieves a list of all supported currency codes
        /// </summary>
        /// <returns>A list of supported currency codes</returns>
        Task<ApiResponse<StringListResponse>> GetSupportedCurrencies();

        /// <summary>
        /// Retrieves a list of all available business lines
        /// </summary>
        /// <returns>A list of business line names</returns>
        Task<ApiResponse<StringListResponse>> GetBusinessLines();

        #endregion
    }
}
