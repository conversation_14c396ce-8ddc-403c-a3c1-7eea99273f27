Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB11E20000 ntdll.dll
7FFB10FF0000 KERNEL32.DLL
7FFB0F640000 KERNELBASE.dll
7FFB111A0000 USER32.dll
7FFB0FB80000 win32u.dll
000210040000 msys-2.0.dll
7FFB104C0000 GDI32.dll
7FFB0F3B0000 gdi32full.dll
7FFB0F9C0000 msvcp_win.dll
7FFB0FA60000 ucrtbase.dll
7FFB105A0000 advapi32.dll
7FFB107F0000 msvcrt.dll
7FFB11540000 sechost.dll
7FFB11790000 RPCRT4.dll
7FFB0E3C0000 CRYPTBASE.DLL
7FFB0FC70000 bcryptPrimitives.dll
7FFB104F0000 IMM32.DLL
