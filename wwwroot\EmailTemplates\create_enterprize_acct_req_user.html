<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Account Creation Request</title>
    <style>
        body {
            font-family: 'Rubik', sans-serif;
            font-size: 16px;
            color: #515151;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            line-height: 1.6;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #07C0EA;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .logo img {
            width: 120px;
            height: auto;
        }

        .tagline {
            color: #fff;
            font-size: 18px;
            font-weight: 500;
            text-align: right;
        }

        .content {
            padding: 30px;
            background-color: #FFFFFF;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .avatar {
            text-align: center;
            margin-bottom: 25px;
        }

            .avatar img {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                object-fit: cover;
                border: 3px solid #07C0EA;
            }

        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
            text-align: center;
            margin-bottom: 25px;
        }

        .details {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 20px;
            margin: 25px 0;
            border-left: 4px solid #07C0EA;
        }

        .details-row {
            margin-bottom: 10px;
            display: flex;
            flex-wrap: wrap;
        }

        .details-label {
            font-weight: 600;
            width: 40%;
            min-width: 150px;
            color: #333;
        }

        .details-value {
            width: 60%;
            color: #555;
        }

        .footer {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-top: 30px;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
        }

        .footer-left, .footer-right {
            padding: 15px;
        }

            .footer-left img {
                width: 100px;
                margin-bottom: 15px;
            }

        .contact-info {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            color: #fff;
        }

            .contact-info a {
                color: #fff;
                text-decoration: underline;
            }

        .social-icons a {
            color: #fff;
            margin-right: 15px;
            font-size: 18px;
            text-decoration: none;
        }

        .app-links img {
            width: 120px;
            margin: 5px 0;
            border-radius: 5px;
        }

        .cta-button {
            display: inline-block;
            background-color: #07C0EA;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            margin-top: 15px;
            text-align: center;
        }

        @media only screen and (max-width: 600px) {
            .container {
                padding: 15px;
            }

            .content {
                padding: 20px 15px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .tagline {
                text-align: center;
                margin-top: 10px;
            }

            .footer {
                flex-direction: column;
            }

            .footer-left, .footer-right {
                width: 100%;
                text-align: center;
            }

            .details-label, .details-value {
                width: 100%;
            }

            .details-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="https://iili.io/26Yp8iX.png" alt="Company Logo">
            </div>
            <div class="tagline">
                Enterprise Account Creation Request
            </div>
        </div>

        <div class="content">
            <div class="avatar">
                <img src="https://iili.io/26YpQxS.gif" alt="Admin Alert">
            </div>

            <div class="message">
                <h2 style="color: #333; margin-top: 0;">Thank you for your request!</h2>
                We have received your request for an enterprise account. Below are the details of your request:
            </div>

            <div class="details">
                <div class="details-row">
                    <div class="details-label">Company Name:</div>
                    <div class="details-value">{companyName}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">Name:</div>
                    <div class="details-value">{fullName}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">TicketID:</div>
                    <div class="details-value">{ticketId}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">Email:</div>
                    <div class="details-value">{email}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">Phone Number:</div>
                    <div class="details-value">{phoneNumber}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">Company Size:</div>
                    <div class="details-value">{companySize}</div>
                </div>
                <div class="details-row">
                    <div class="details-label">Application:</div>
                    <div class="details-value">{application}</div>
                </div>
            </div>

            <div class="message">
                Your request is being processed. One of our support staff will reach out to you shortly.
            </div>
        </div>

        <div class="footer">
            <div class="footer-left">
                <img src="https://iili.io/24UR7Kg.png" alt="Company Logo">
                <div class="contact-info">
                    Have a question?<br>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="social-icons">
                    <a href="{linkedin-link}" target="_blank"><i class="fab fa-linkedin"></i></a>
                    <a href="{twitter-link}" target="_blank"><i class="fab fa-twitter"></i></a>
                    <a href="{facebook-link}" target="_blank"><i class="fab fa-facebook"></i></a>
                </div>
            </div>
            <div class="footer-right">
                <div class="app-links">
                    <img src="https://iili.io/24U1VFS.png" alt="Google Play">
                    <img src="https://iili.io/24U1Cua.png" alt="App Store">
                </div>
            </div>
        </div>
    </div>
</body>
</html>
