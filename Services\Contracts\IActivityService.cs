using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Enums;


namespace SuperAdmin.Service.Services.Contracts;

public interface IActivityService
{
    Task<ApiResponse<List<MonthlyActivityCount>>> GetActivitiesStatistics(string? subdomain, Applications application);
    Task<ApiResponse<Page<ActivityCountPerTenant>>> GetTenantsCounts(string? subdomain, ActivityQueryParameters queryParameters);
}