﻿using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Models.Dtos.EnterpriseDomain
{
    public class UpdateEnterprisePlanDto
    {
        public Applications Application { get; set; }
        public PaymentProviders? Provider { get; set; }

        [Required]
        public double AmountPaid { get; set; }
        [Required]
        public string EnterPriseSubId { get; set; }
        [Required]
        public string TenantId { get; set; }
        public int MaxNumberofUser { get; set; }
        public bool IsNumberOfUserUnlimited { get; set; }
        public int CalenderLimit { get; set; }
        public int StorageInGigaByte { get; set; }
        public bool IsStorageUnlimited { get; set; }
        public int CommunicationHistoryInMonth { get; set; }
        public bool KeepCommunicationForever { get; set; }
        public int ProjectCreation { get; set; }
        public bool KeepProjectCreationForever { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        public bool KeepDataRetentionForever { get; set; }
        public bool AIAssistance { get; set; }
        public bool UnlimitedActivityLog { get; set; }
        public bool TimeSheetManagement { get; set; }
    }
}
