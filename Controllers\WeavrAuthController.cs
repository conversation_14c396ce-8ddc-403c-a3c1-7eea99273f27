﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Models.Dtos.WeavrDomain;
using SuperAdmin.Service.Services.Contracts;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = HelperConstants.SUPER_ADMIN_ROLE, AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class WeavrAuthController : BaseController
    {
        private readonly IWeavrClientService _weavrClientService;

        public WeavrAuthController(IWeavrClientService weavrClientService)
        {
            _weavrClientService = weavrClientService;
        }

        [HttpPost("login")]
        public async Task<IActionResult> LoginWithPassword(LoginRequest model)
        {
            var response = await _weavrClientService.LoginWithPassword(model);
            return ParseResponse(response);
        }

        [HttpPost("step-up/challenges/otp")]
        public async Task<IActionResult> InitiateStepUpToken([FromHeader] string token)
        {
            var response = await _weavrClientService.StepUpTokenChallenge(token);
            return ParseResponse(response);
        }

        [HttpPost("step-up/challenges/otp/verify")]
        public async Task<IActionResult> VerifyStepUpToken([FromHeader] string token, [FromBody] StepUpVerification model)
        {
            var response = await _weavrClientService.VerifyStepUpToken(token, model);
            return ParseResponse(response);
        }
    }
}
