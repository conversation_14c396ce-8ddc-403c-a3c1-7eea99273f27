using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain
{
    /// <summary>
    /// Data transfer object representing a currency conversion rate between two currencies
    /// </summary>
    public class CurrencyConversionRateDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for this currency conversion rate
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the application this conversion rate applies to
        /// </summary>
        public string Application { get; set; }

        /// <summary>
        /// Gets or sets the source currency being converted from
        /// </summary>
        public string FromCurrency { get; set; }

        /// <summary>
        /// Gets or sets the target currency being converted to
        /// </summary>
        public string ToCurrency { get; set; }
        /// <summary>
        /// Gets or sets the exchange rate for converting from the source to target currency
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Gets or sets the date when this conversion rate becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the optional expiry date for this conversion rate
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this conversion rate is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the source of this conversion rate (e.g., "Manual", "API", "Bank")
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created this conversion rate
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who last modified this conversion rate
        /// </summary>
        public string ModifiedBy { get; set; }

        /// <summary>
        /// Gets or sets the date and time when this conversion rate was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time when this conversion rate was last modified
        /// </summary>
        public DateTime ModifiedAt { get; set; }
    }

    /// <summary>
    /// Data transfer object for creating a new currency conversion rate
    /// </summary>
    public class CreateCurrencyConversionRateDto
    {
        /// <summary>
        /// Gets or sets the application this conversion rate applies to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the source currency being converted from
        /// </summary>
        public CurrencyCode FromCurrency { get; set; }

        /// <summary>
        /// Gets or sets the target currency being converted to
        /// </summary>
        public CurrencyCode ToCurrency { get; set; }

        /// <summary>
        /// Gets or sets the exchange rate for the conversion
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Gets or sets the date when this conversion rate becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the optional expiry date for this conversion rate
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Gets or sets the source of this conversion rate
        /// </summary>
        public string Source { get; set; }
    }

    public class UpdateCurrencyConversionRateDto
    {
        public decimal ExchangeRate { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Source { get; set; }
    }

    public class CurrencyConversionRateFilterDto
    {
        public Applications? Application { get; set; }
        public CurrencyCode? FromCurrency { get; set; }
        public CurrencyCode? ToCurrency { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? EffectiveDateFrom { get; set; }
        public DateTime? EffectiveDateTo { get; set; }
        public string? Source { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class BulkCurrencyRateImportDto
    {
        public List<CurrencyRateImportItem> Rates { get; set; } = new List<CurrencyRateImportItem>();
    }

    public class CurrencyRateImportItem
    {
        public string FromCurrency { get; set; }
        public string ToCurrency { get; set; }
        public decimal ExchangeRate { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string Source { get; set; }
    }

    public class BulkImportResultDto
    {
        public int TotalRecords { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<ImportErrorDto> Errors { get; set; } = new List<ImportErrorDto>();
    }

    public class ImportErrorDto
    {
        public int RowNumber { get; set; }
        public string Error { get; set; }
        public CurrencyRateImportItem Data { get; set; }
    }
}
