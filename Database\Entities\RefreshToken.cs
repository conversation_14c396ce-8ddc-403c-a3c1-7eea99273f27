﻿using SuperAdmin.Service.Models.Dtos.WeavrDomain;

namespace SuperAdmin.Service.Database.Entities
{
    public class RefreshToken : BaseEntity
    {
        public RefreshToken()
        {
            Id = Guid.NewGuid();
        }

        public Guid Id { get; set; }
        public string UserId { get; set; }
        public string Token { get; set; }
        public string JwtId { get; set; }
        public bool IsUsed { get; set; }
        public bool IsRevoked { get; set; }
        public DateTime ExpiryDate { get; set; }

    }
}
