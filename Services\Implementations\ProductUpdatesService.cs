﻿using Common.Services.Interfaces;
using Configurations.Utility;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using RabbitMQ.Client;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.ProductUpdateDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.RabbitMQ;
using SuperAdmin.Service.RabbitMQ.MessagePayload;
using SuperAdmin.Service.Services.Contracts;

namespace SuperAdmin.Service.Services.Implementations
{
    public class ProductUpdatesService : IProductUpdatesService
    {
        private readonly AppDbContext _appDbContext;
        private readonly IGCPBucketService _gcpBucketService;
        private readonly IPublisherService _publisherService;
        private readonly ILogger<ProductUpdatesService> _logger;
        private readonly IActivityLogService _activityLogService;
        private readonly UserManager<User> _userManager;

        public ProductUpdatesService(AppDbContext appDbContext, IGCPBucketService gcpBucketService, IPublisherService publisherService,
            ILogger<ProductUpdatesService> logger, IActivityLogService activityLogService, UserManager<User> userManager)
        {
            _appDbContext = appDbContext;
            _gcpBucketService = gcpBucketService;
            _publisherService = publisherService;
            _logger = logger;
            _activityLogService = activityLogService;
            _userManager = userManager;
        }

        /// <summary>
        /// This method creates a new product update category
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<ApiResponse<dynamic>> CreateProductUpdateCategory(string category, string userId, Applications? applications)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            if (string.IsNullOrWhiteSpace(category))
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide category name"
                };
            }

            string trimmedCategory = category.Trim().ToLower();
            var existingCategory = await _appDbContext.ProductUpdateCategories.FirstOrDefaultAsync(x => x.Name == trimmedCategory);

            if (existingCategory is not null && existingCategory.IsDeleted)
            {
                existingCategory.IsDeleted = true;
                existingCategory.ModifiedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _activityLogService.LogActivity(ActivityActionType.Creation, "",
                $"{user.FirstName} created product update category", "", user.Id, applications);

                return new ApiResponse<dynamic>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Created category successfully"
                };
            }
            else if (existingCategory is not null && !existingCategory.IsDeleted)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseMessage = "Category already exists",
                    ResponseCode = "200"
                };
            }
            else
            {
                _appDbContext.ProductUpdateCategories.Add(new ProductUpdateCategory
                {
                    Name = trimmedCategory,
                });

                await _appDbContext.SaveChangesAsync();

                await _activityLogService.LogActivity(ActivityActionType.Creation, "",
               $"{user.FirstName} created product update category", "", user.Id, applications);

                return new ApiResponse<dynamic>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Created category successfully"
                };
            }
        }

        /// <summary>
        /// This method gets all job suite packages
        /// </summary>
        /// <returns></returns>
        public ApiResponse<IEnumerable<string>> GetPackages()
        {
            var packages = Enum.GetNames(typeof(JobSuitePackage)).ToList();

            //await _activityLogService.LogActivity(ActivityActionType.Creation, "",
            //   $"{user.FirstName} created product update category", "", user.Id);

            return new ApiResponse<IEnumerable<string>>
            {
                Data = packages,
                ResponseCode = "200"
            };
        }

        /// <summary>
        /// This method gets all product update categories
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<IEnumerable<ProductUpdateCategoryDto>>> GetProductUpdateCategories()
        {
            var productCategories = await _appDbContext.ProductUpdateCategories.Where(x => !x.IsDeleted)
                                                                                                        .Select(x => new ProductUpdateCategoryDto
                                                                                                        {
                                                                                                            CategoryId = x.Id,
                                                                                                            CategoryName = x.Name
                                                                                                        }).ToListAsync();


            return new ApiResponse<IEnumerable<ProductUpdateCategoryDto>>
            {
                Data = productCategories,
                ResponseCode = "200"
            };
        }

        /// <summary>
        /// This method sends a product update or saves product update as draft depending on the publish command
        /// </summary>
        /// <param name="publishCommand"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> CreateProductUpdate(PublishCommand publishCommand, CreateProductUpdateDto model, string userId)
        {
            if (!model.CategoryId.HasValue &&
                string.IsNullOrWhiteSpace(model.Subject) &&
                string.IsNullOrWhiteSpace(model.Body) &&
                !model.Package.HasValue && model.ImageAttachment is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide parameters"
                };
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            switch (publishCommand)
            {
                case PublishCommand.SaveAsDraft:
                    (bool isSuccessful, string? message) = await SaveProductUpdateAsDraft(model);
                    if (!isSuccessful)
                    {
                        return new ApiResponse<dynamic>
                        {
                            ResponseCode = "400",
                            ResponseMessage = message
                        };
                    }

                    await _activityLogService.LogActivity(ActivityActionType.Creation, "",
                    $"{user.FirstName} created product update and saved as draft", "", user.Id, model.Applications);

                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Product update successfully saved as draft"
                    };

                case PublishCommand.Publish:
                    (isSuccessful, message) = await SendProductUpdate(model);
                    if (!isSuccessful)
                    {
                        return new ApiResponse<dynamic>
                        {
                            ResponseCode = "400",
                            ResponseMessage = message
                        };
                    }

                    await _activityLogService.LogActivity(ActivityActionType.Publish, "",
                   $"{user.FirstName} published product update", "", user.Id, model.Applications);

                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Successfully published product update"
                    };

                default:
                    return new ApiResponse<dynamic>
                    {
                        ResponseMessage = "Please provide valid publish command type"
                    };
            }
        }

        /// <summary>
        /// This method gets all sent product updates
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<PaginationResult<SentProductUpdateDto>>> GetSentProductUpdates(FilterProductUpdatesBy filterBy, string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<PaginationResult<SentProductUpdateDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            IQueryable<SentProductUpdateDto> productUpdates;
            if (!string.IsNullOrWhiteSpace(filterBy.Category))
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Sent && x.ProductUpdateCategory.Name == filterBy.Category)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new SentProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.CreatedAt
                                        });
            }
            else
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Sent)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new SentProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.CreatedAt
                                        });
            }

            productUpdates = SortProductUpdatesBy(filterBy.SortBy, productUpdates);
            var data = await PaginationHelper.PaginateRecords(productUpdates, filterBy.Page!.Value, filterBy.PageSize!.Value);
            var update = new List<ProductUpdate>();
            foreach (var item in data.Items)
            {
                if (!string.IsNullOrEmpty(item.FileName) && (DateTime.UtcNow - item.ModifiedAt).TotalMinutes > 5)
                {
                    var url = await _gcpBucketService.GetSignedUrlAsync(item.FileName);
                    item.ImageAttachmentUrl = url;
                    item.ModifiedAt = DateTime.UtcNow;

                    update.Add(new ProductUpdate() { Id = item.Id, ImageUrl = url, ModifiedAt = DateTime.UtcNow });
                }
            }
            _appDbContext.UpdateRange(update);
            await _appDbContext.SaveChangesAsync();
            MapDateToEpochTimestamp(data.Items);

            await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
            $"{user.FirstName} Viewed sent product update", "", user.Id, filterBy.Applications);

            return new ApiResponse<PaginationResult<SentProductUpdateDto>>
            {
                Data = data,
                ResponseCode = "200"
            };
        }

        /// <summary>
        /// This method gets all sent product updates
        /// </summary>
        /// <returns></returns>
        public async Task<ApiResponse<PaginationResult<SentProductUpdateDto>>> GetSentProductUpdatesForAdmin(FilterProductUpdatesBy filterBy)
        {           

            IQueryable<SentProductUpdateDto> productUpdates;
            if (!string.IsNullOrWhiteSpace(filterBy.Category))
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Sent && x.ProductUpdateCategory.Name == filterBy.Category)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new SentProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.CreatedAt
                                        });
            }
            else
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Sent)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new SentProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.CreatedAt
                                        });
            }

            productUpdates = SortProductUpdatesBy(filterBy.SortBy, productUpdates);
            var data = await PaginationHelper.PaginateRecords(productUpdates, filterBy.Page!.Value, filterBy.PageSize!.Value);
            var update = new List<ProductUpdate>();
            foreach (var item in data.Items)
            {
                if (!string.IsNullOrEmpty(item.FileName) && (DateTime.UtcNow - item.ModifiedAt).TotalMinutes > 5)
                {
                    var url = await _gcpBucketService.GetSignedUrlAsync(item.FileName);
                    item.ImageAttachmentUrl = url;
                    item.ModifiedAt = DateTime.UtcNow;

                    update.Add(new ProductUpdate() { Id = item.Id, ImageUrl = url, ModifiedAt = DateTime.UtcNow });
                }
            }
            _appDbContext.UpdateRange(update);
            await _appDbContext.SaveChangesAsync();
            MapDateToEpochTimestamp(data.Items);           

            return new ApiResponse<PaginationResult<SentProductUpdateDto>>
            {
                Data = data,
                ResponseCode = "200"
            };
        }

        /// <summary>
        /// This method gets all draft product updates
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PaginationResult<DraftProductUpdateDto>>> GetDraftProductUpdates(FilterProductUpdatesBy filterBy, string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<PaginationResult<DraftProductUpdateDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            IQueryable<DraftProductUpdateDto> productUpdates;
            if (!string.IsNullOrWhiteSpace(filterBy.Category))
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Draft && x.ProductUpdateCategory.Name == filterBy.Category)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new DraftProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.ModifiedAt,
                                            FileName = x.FileName,
                                            ModifiedAt = x.ModifiedAt
                                        });
            }
            else
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Draft)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new DraftProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.ModifiedAt,
                                            FileName = x.FileName,
                                            ModifiedAt = x.ModifiedAt
                                        });
            }

            productUpdates = SortProductUpdatesBy(filterBy.SortBy, productUpdates);
            var data = await PaginationHelper.PaginateRecords(productUpdates, filterBy.Page!.Value, filterBy.PageSize!.Value);
            var update = new List<ProductUpdate>();
            foreach (var item in data.Items)
            {
                if (!string.IsNullOrEmpty(item.FileName) && (DateTime.UtcNow - item.ModifiedAt).TotalMinutes > 5)
                {
                    var url = await _gcpBucketService.GetSignedUrlAsync(item.FileName);
                    item.ImageAttachmentUrl = url;
                    item.ModifiedAt = DateTime.UtcNow;

                    //update.Add(new ProductUpdate() { Id = item.Id, ImageUrl = url, ModifiedAt = DateTime.UtcNow, FileName = item.FileName });
                    var proDuctUpd = await _appDbContext.ProductUpdates.FirstOrDefaultAsync(x => x.Id == item.Id);
                    if (proDuctUpd != null)
                    {
                        proDuctUpd.ModifiedAt = DateTime.UtcNow;
                        proDuctUpd.ImageUrl = url;
                        proDuctUpd.FileName = item.FileName;
                        _appDbContext.Update(proDuctUpd);
                    }
                }
            }
            // _appDbContext.UpdateRange(update);
            await _appDbContext.SaveChangesAsync();
            MapDateToEpochTimestamp(data.Items);

            await _activityLogService.LogActivity(Database.Enums.ActivityActionType.View, "",
           $"{user.FirstName} Viewed draft product update", "", user.Id, filterBy.Applications);

            return new ApiResponse<PaginationResult<DraftProductUpdateDto>>
            {
                Data = data,
                ResponseCode = "200"
            };
        }

        /// <summary>
        /// This method gets all deleted product updates
        /// </summary>
        /// <param name="filterBy"></param>
        /// <returns></returns>
        public async Task<ApiResponse<PaginationResult<DeletedProductUpdateDto>>> GetDeletedProductUpdates(FilterProductUpdatesBy filterBy, string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<PaginationResult<DeletedProductUpdateDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            IQueryable<DeletedProductUpdateDto> productUpdates;
            if (!string.IsNullOrWhiteSpace(filterBy.Category))
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Deleted && x.ProductUpdateCategory.Name == filterBy.Category)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new DeletedProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.ModifiedAt
                                        });
            }
            else
            {
                productUpdates = _appDbContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Deleted)
                                        .Include(x => x.ProductUpdateCategory)
                                        .Select(x => new DeletedProductUpdateDto
                                        {
                                            Id = x.Id,
                                            Body = x.Body,
                                            CategoryName = x.ProductUpdateCategory.Name,
                                            Package = x.Package,
                                            Subject = x.Subject,
                                            ImageAttachmentUrl = x.ImageUrl,
                                            CreatedAt = x.ModifiedAt
                                        });
            }

            productUpdates = SortProductUpdatesBy(filterBy.SortBy, productUpdates);
            var data = await PaginationHelper.PaginateRecords(productUpdates, filterBy.Page!.Value, filterBy.PageSize!.Value);
            var update = new List<ProductUpdate>();
            foreach (var item in data.Items)
            {
                if (!string.IsNullOrEmpty(item.FileName) && (DateTime.UtcNow - item.ModifiedAt).TotalMinutes > 5)
                {
                    var url = await _gcpBucketService.GetSignedUrlAsync(item.FileName);
                    item.ImageAttachmentUrl = url;
                    item.ModifiedAt = DateTime.UtcNow;

                    update.Add(new ProductUpdate() { Id = item.Id, ImageUrl = url, ModifiedAt = DateTime.UtcNow });
                }
            }
            _appDbContext.UpdateRange(update);
            await _appDbContext.SaveChangesAsync();


            await _activityLogService.LogActivity(ActivityActionType.View, "",
            $"{user.FirstName} viewed deleted product update", "", user.Id, filterBy.Applications); 
            
            MapDateToEpochTimestamp(data.Items);
            return new ApiResponse<PaginationResult<DeletedProductUpdateDto>>
            {
                Data = data,
                ResponseCode = "200"
            };
        }

       
        /// <summary>
        /// This method updates a draft product update
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> UpdateDraftProductUpdates(Guid id, CreateProductUpdateDto model, string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            if (!model.CategoryId.HasValue &&
                string.IsNullOrWhiteSpace(model.Subject) &&
                string.IsNullOrWhiteSpace(model.Body) &&
                !model.Package.HasValue && model.ImageAttachment is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide parameters"
                };
            }

            var draftProductUpdate = await _appDbContext.ProductUpdates.Where(x => x.Id == id && x.PublishStatus == PublishStatus.Draft).SingleOrDefaultAsync();
            if (draftProductUpdate is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Draft product update not found"
                };
            }

            if (model.CategoryId.HasValue)
            {
                var categoryExists = await _appDbContext.ProductUpdateCategories.AnyAsync(x => x.Id == model.CategoryId.Value);
                if (!categoryExists)
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Category not found"
                    };
                }

                draftProductUpdate.CategoryId = model.CategoryId.Value;
            }

            if (!string.IsNullOrWhiteSpace(model.Subject))
            {
                draftProductUpdate.Subject = model.Subject.Trim();
            }

            if (!string.IsNullOrWhiteSpace(model.Body))
            {
                draftProductUpdate.Body = model.Body.Trim();
            }

            if (model.ImageAttachment != null)
            {
                // (bool isSuccessful, string? message) = ValidateFileExtension(model.ImageAttachment);
                // if (!isSuccessful)
                // {
                //     return new ApiResponse<dynamic>
                //     {
                //         ResponseCode = "400",
                //         ResponseMessage = message
                //     };
                // }

                (bool isSuccessful, string? message) = ValidateFileSize(model.ImageAttachment);
                if (!isSuccessful)
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = message
                    };
                }

                string fileNameToSave = $"attachment_{draftProductUpdate.Id}_{DateTime.UtcNow.ToEpochTimestampInMilliseconds()}";
                var fileUploadUrl = await _gcpBucketService.UploadFileAsync(model.ImageAttachment, fileNameToSave);
                if (string.IsNullOrWhiteSpace(fileUploadUrl))
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Unable to save attachment. Please try again"
                    };
                }

                var imageUrl = await _gcpBucketService.GetSignedUrlAsync(fileNameToSave);
                draftProductUpdate.FileName = fileNameToSave;
                draftProductUpdate.ImageUrl = imageUrl;
            }

            if (model.Package.HasValue && Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
            {
                if (!Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Package not found"
                    };
                }

                draftProductUpdate.Package = model.Package.Value.ToString();
            }

            draftProductUpdate.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();


            await _activityLogService.LogActivity(ActivityActionType.Modification, "",
            $"{user.FirstName} updated product update", "", user.Id, model.Applications);

            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = "Draft updated successfully"
            };
        }

        /// <summary>
        /// This method sends out a draft product updates
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> SendDraftProductUpdates(Guid id, CreateProductUpdateDto model, string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            var draftProductUpdate = await _appDbContext.ProductUpdates.Where(x => x.Id == id && x.PublishStatus == PublishStatus.Draft)
                                                                                    .Include(x => x.ProductUpdateCategory)
                                                                                    .SingleOrDefaultAsync();
            if (draftProductUpdate is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Draft product update not found"
                };
            }

            if (model.CategoryId.HasValue)
            {
                var categoryExists = await _appDbContext.ProductUpdateCategories.AnyAsync(x => x.Id == model.CategoryId.Value);
                if (!categoryExists)
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Category not found"
                    };
                }

                draftProductUpdate.CategoryId = model.CategoryId.Value;
            }

            if (!string.IsNullOrWhiteSpace(model.Subject))
            {
                draftProductUpdate.Subject = model.Subject.Trim();
            }

            if (!string.IsNullOrWhiteSpace(model.Body))
            {
                draftProductUpdate.Body = model.Body.Trim();
            }

            if (model.ImageAttachment != null)
            {
                // (bool isSuccessful, string? message) = ValidateFileExtension(model.ImageAttachment);
                // if (!isSuccessful)
                // {
                //     return new ApiResponse<dynamic>
                //     {
                //         ResponseCode = "400",
                //         ResponseMessage = message
                //     };
                // }

                (bool isSuccessful, string? message) = ValidateFileSize(model.ImageAttachment);
                if (!isSuccessful)
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = message
                    };
                }

                string fileNameToSave = $"attachment_{draftProductUpdate.Id}_{DateTime.UtcNow.ToEpochTimestampInMilliseconds()}";
                var fileUploadUrl = await _gcpBucketService.UploadFileAsync(model.ImageAttachment, fileNameToSave);
                if (string.IsNullOrWhiteSpace(fileUploadUrl))
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Unable to save attachment. Please try again"
                    };
                }

                var imageUrl = await _gcpBucketService.GetSignedUrlAsync(fileNameToSave);
                draftProductUpdate.FileName = fileNameToSave;
                draftProductUpdate.ImageUrl = imageUrl;
            }

            if (model.Package.HasValue && Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
            {
                if (!Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Package not found"
                    };
                }

                draftProductUpdate.Package = model.Package.Value.ToString();
            }

            if (string.IsNullOrWhiteSpace(draftProductUpdate.Subject)
                || string.IsNullOrWhiteSpace(draftProductUpdate.Body)
                || string.IsNullOrWhiteSpace(draftProductUpdate.Package)
                || draftProductUpdate.CategoryId == Guid.Empty)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide parameters"
                };
            }

            draftProductUpdate.PublishStatus = PublishStatus.Sent;
            draftProductUpdate.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            var payload = draftProductUpdate.MapToProductUpdatePayload();
            var eventModel = new PublishRecord
            (
            RabbitMQConstants.PublishProductUpdateEvent,
            RabbitMQConstants.ProductUpdateKey,
            ExchangeType.Direct,
            new List<string> { RabbitMQConstants.ProductUpdateQueue },
            payload
            );

            bool isPublished = await _publisherService.GenericPublish(eventModel);
            if (!isPublished)
            {
                _logger.LogError("Product-update-created-event could not be published");
            }

            await _activityLogService.LogActivity(ActivityActionType.Publish, "",
                       $"{user.FirstName} published product update", "", user.Id, model.Applications);

            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = "Successfully published product update"
            };
        }

        /// <summary>
        /// This method deletes a list of product updates
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> DeleteProductUpdates(List<Guid> ids, string userId, Applications? applications)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user is null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found"
                };
            }

            if (!ids.Any())
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide parameters"
                };
            }

            var productUpdates = _appDbContext.ProductUpdates.Include(x => x.ProductUpdateCategory)
                                                                                .Where(x => ids.Contains(x.Id) && x.PublishStatus != PublishStatus.Deleted);
            if (!await productUpdates.AnyAsync())
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Product updates not found"
                };
            }

            List<ProductUpdateMessage> payloads = new List<ProductUpdateMessage>();
            foreach (var item in productUpdates)
            {
                item.PublishStatus = PublishStatus.Deleted;
                item.ModifiedAt = DateTime.UtcNow;

                payloads.Add(item.MapToProductUpdatePayload());
            }

            await _appDbContext.SaveChangesAsync();

            foreach (var payload in payloads)
            {
                var eventModel = new PublishRecord
                (
                RabbitMQConstants.PublishProductUpdateEvent,
                RabbitMQConstants.ProductUpdateKey,
                ExchangeType.Direct,
                new List<string> { RabbitMQConstants.ProductUpdateQueue },
                payload
                );

                bool isPublished = await _publisherService.GenericPublish(eventModel);
                if (!isPublished)
                {
                    _logger.LogError("publish-product-update-event could not be published");
                }
            }

            await _activityLogService.LogActivity(ActivityActionType.Deletion, "",
            $"{user.FirstName} deleted product update", "", user.Id, applications);

            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = "Product updates deleted successfully"
            };
        }

        /// <summary>
        /// This method deletes a list of product updates
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApiResponse<dynamic>> DeleteProductUpdatesForAdmin(List<Guid> ids)
        {
            if (!ids.Any())
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide parameters"
                };
            }

            var productUpdates = _appDbContext.ProductUpdates.Include(x => x.ProductUpdateCategory)
                                                                                .Where(x => ids.Contains(x.Id) && x.PublishStatus != PublishStatus.Deleted);
            if (!await productUpdates.AnyAsync())
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Product updates not found"
                };
            }

            List<ProductUpdateMessage> payloads = new List<ProductUpdateMessage>();
            foreach (var item in productUpdates)
            {
                item.PublishStatus = PublishStatus.Deleted;
                item.ModifiedAt = DateTime.UtcNow;

                payloads.Add(item.MapToProductUpdatePayload());
            }

            await _appDbContext.SaveChangesAsync();

            foreach (var payload in payloads)
            {
                var eventModel = new PublishRecord
                (
                RabbitMQConstants.PublishProductUpdateEvent,
                RabbitMQConstants.ProductUpdateKey,
                ExchangeType.Direct,
                new List<string> { RabbitMQConstants.ProductUpdateQueue },
                payload
                );

                bool isPublished = await _publisherService.GenericPublish(eventModel);
                if (!isPublished)
                {
                    _logger.LogError("publish-product-update-event could not be published");
                }
            }

            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = "Product updates deleted successfully"
            };
        }

        /// <summary>
        /// This private method saves product update as draft
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<(bool, string?)> SaveProductUpdateAsDraft(CreateProductUpdateDto model)
        {
            ProductUpdate productUpdate = new ProductUpdate();

            if (model.CategoryId.HasValue)
            {
                var categoryExists = await _appDbContext.ProductUpdateCategories.AnyAsync(x => x.Id == model.CategoryId.Value);
                if (!categoryExists)
                {
                    return (false, "Category not found");
                }

                productUpdate.CategoryId = model.CategoryId.Value;
            }
            else
            {
                return (false, "Please provide category");
            }

            if (!string.IsNullOrWhiteSpace(model.Subject))
            {
                productUpdate.Subject = model.Subject.Trim();
            }

            if (!string.IsNullOrWhiteSpace(model.Body))
            {
                productUpdate.Body = model.Body.Trim();
            }

            if (model.ImageAttachment != null)
            {
                // (bool isSuccessful, string? message) = ValidateFileExtension(model.ImageAttachment);
                // if (!isSuccessful)
                // {
                //     return (isSuccessful, message);
                // }

                (bool isSuccessful, string? message) = ValidateFileSize(model.ImageAttachment);
                if (!isSuccessful)
                {
                    return (isSuccessful, message);
                }

                string fileNameToSave = $"attachment_{productUpdate.Id}_{DateTime.UtcNow.ToEpochTimestampInMilliseconds()}";
                var fileUploadUrl = await _gcpBucketService.UploadFileAsync(model.ImageAttachment, fileNameToSave);
                if (string.IsNullOrWhiteSpace(fileUploadUrl))
                {
                    return (false, "Unable to save attachment. Please try again");
                }

                var imageUrl = await _gcpBucketService.GetSignedUrlAsync(fileNameToSave);
                productUpdate.FileName = fileNameToSave;
                productUpdate.ImageUrl = imageUrl;
            }

            if (model.Package.HasValue)
            {
                if (!Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
                {
                    return (false, "Package not found");
                }

                productUpdate.Package = model.Package.Value.ToString();
            }

            productUpdate.PublishStatus = PublishStatus.Draft;
            _appDbContext.Add(productUpdate);
            await _appDbContext.SaveChangesAsync();

            return (true, null);
        }

        /// <summary>
        /// This private method sends product updates
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private async Task<(bool, string?)> SendProductUpdate(CreateProductUpdateDto model)
        {
            ProductUpdate productUpdate = new ProductUpdate();

            if (model.CategoryId.HasValue)
            {
                var categoryExists = await _appDbContext.ProductUpdateCategories.AnyAsync(x => x.Id == model.CategoryId.Value);
                if (!categoryExists)
                {
                    return (false, "Category not found");
                }

                productUpdate.CategoryId = model.CategoryId.Value;
            }
            else
            {
                return (false, "Please provide category");
            }

            if (model.Package.HasValue && Enum.IsDefined(typeof(JobSuitePackage), model.Package.Value))
            {
                productUpdate.Package = model.Package.Value.ToString();
            }
            else
            {
                return (false, "Please provide packages");
            }

            if (!string.IsNullOrWhiteSpace(model.Subject))
            {
                productUpdate.Subject = model.Subject.Trim();
            }
            else
            {
                return (false, "Please provide subject text");
            }

            if (!string.IsNullOrWhiteSpace(model.Body))
            {
                productUpdate.Body = model.Body.Trim();
            }
            else
            {
                return (false, "Please provide body text");
            }

            if (model.ImageAttachment is not null)
            {
                // (bool isSuccessful, string? message) = ValidateFileExtension(model.ImageAttachment);
                // if (!isSuccessful)
                // {
                //     return (isSuccessful, message);
                // }

                (bool isSuccessful, string? message) = ValidateFileSize(model.ImageAttachment);
                if (!isSuccessful)
                {
                    return (isSuccessful, message);
                }

                string fileNameToSave = $"attachment_{productUpdate.Id}_{DateTime.UtcNow.ToEpochTimestampInMilliseconds()}";
                var fileUploadUrl = await _gcpBucketService.UploadFileAsync(model.ImageAttachment, fileNameToSave);
                if (string.IsNullOrWhiteSpace(fileUploadUrl))
                {
                    return (false, "Unable to save attachment. Please try again");
                }

                var imageUrl = await _gcpBucketService.GetSignedUrlAsync(fileNameToSave);
                productUpdate.FileName = fileNameToSave;
                productUpdate.ImageUrl = imageUrl;
            }

            productUpdate.PublishStatus = PublishStatus.Sent;
            _appDbContext.ProductUpdates.Add(productUpdate);
            await _appDbContext.SaveChangesAsync();

            var payload = productUpdate.MapToProductUpdatePayload();
            var eventModel = new PublishRecord
            (
            RabbitMQConstants.PublishProductUpdateEvent,
            RabbitMQConstants.ProductUpdateKey,
            ExchangeType.Direct,
            new List<string> { RabbitMQConstants.ProductUpdateQueue },
            payload
            );

            bool isPublished = await _publisherService.GenericPublish(eventModel);
            if (!isPublished)
            {
                _logger.LogError("publish-product-update-event could not be published");
            }

            return (true, null);
        }

        /// <summary>
        /// This private method orders product update records
        /// </summary>
        /// <param name="sortBy"></param>
        /// <param name="productUpdates"></param>
        /// <returns></returns>
        private IQueryable<SentProductUpdateDto> SortProductUpdatesBy(SortProductUpdateBy? sortBy, IQueryable<SentProductUpdateDto> productUpdates)
        {
            if (!sortBy.HasValue)
            {
                return productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
            }

            switch (sortBy.Value)
            {
                case SortProductUpdateBy.NewestFirst:
                    productUpdates = productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
                    break;

                case SortProductUpdateBy.OldestFirst:
                    productUpdates = productUpdates.OrderBy(x => x.CreatedAt).AsQueryable();
                    break;
            }

            return productUpdates;
        }

        private IQueryable<DraftProductUpdateDto> SortProductUpdatesBy(SortProductUpdateBy? sortBy, IQueryable<DraftProductUpdateDto> productUpdates)
        {
            if (!sortBy.HasValue)
            {
                return productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
            }

            switch (sortBy.Value)
            {
                case SortProductUpdateBy.NewestFirst:
                    productUpdates = productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
                    break;

                case SortProductUpdateBy.OldestFirst:
                    productUpdates = productUpdates.OrderBy(x => x.CreatedAt).AsQueryable();
                    break;
            }

            return productUpdates;
        }

        private IQueryable<DeletedProductUpdateDto> SortProductUpdatesBy(SortProductUpdateBy? sortBy, IQueryable<DeletedProductUpdateDto> productUpdates)
        {
            if (!sortBy.HasValue)
            {
                return productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
            }

            switch (sortBy.Value)
            {
                case SortProductUpdateBy.NewestFirst:
                    productUpdates = productUpdates.OrderByDescending(x => x.CreatedAt).AsQueryable();
                    break;

                case SortProductUpdateBy.OldestFirst:
                    productUpdates = productUpdates.OrderBy(x => x.CreatedAt).AsQueryable();
                    break;
            }

            return productUpdates;
        }

        private void MapDateToEpochTimestamp(SentProductUpdateDto[] records)
        {
            foreach (var record in records)
            {
                record.DateSentInMilliseconds = record.CreatedAt.ToEpochTimestampInMilliseconds();
            }
        }

        private void MapDateToEpochTimestamp(DraftProductUpdateDto[] records)
        {
            foreach (var record in records)
            {
                record.DateEditedInMilliseconds = record.CreatedAt.ToEpochTimestampInMilliseconds();
            }
        }

        private void MapDateToEpochTimestamp(DeletedProductUpdateDto[] records)
        {
            foreach (var record in records)
            {
                record.DateDeletedInMilliseconds = record.CreatedAt.ToEpochTimestampInMilliseconds();
            }
        }

        private static (bool, string?) ValidateFileSize(IFormFile file)
        {
            long fileSizeInKb = file.Length / HelperConstants.BytesToKbRatio;

            if (fileSizeInKb > HelperConstants.MaxImageFileSizeInKb)
            {
                return (false, $"Attachment size of {fileSizeInKb}kb cannot be uploaded. Max allowed size is {HelperConstants.MaxImageFileSizeInKb}kb");
            }

            return (true, null);
        }

        private static (bool, string?) ValidateFileExtension(IFormFile file)
        {
            string extension = Path.GetExtension(file.FileName);

            if (!extension.Equals(".png", StringComparison.OrdinalIgnoreCase))
            {
                return (false, "Unsupported file format");
            }

            return (true, null);
        }
    }
}
