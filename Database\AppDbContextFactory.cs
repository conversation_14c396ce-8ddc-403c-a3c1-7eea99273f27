using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace SuperAdmin.Service.Database
{
    //public class AppDbContextFactory : IDesignTimeDbContextFactory<AppDbContext>
    //{
    //    public AppDbContext CreateDbContext(string[] args)
    //    {
    //        var configuration = new ConfigurationBuilder()
    //            .SetBasePath(Directory.GetCurrentDirectory())
    //            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    //            .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
    //            .Build();

    //        var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
            
    //        // Use the connection string from appsettings.json
    //        var connectionString = configuration.GetConnectionString("ConnectionString");
    //        optionsBuilder.UseNpgsql(connectionString);

    //        return new AppDbContext(optionsBuilder.Options);
    //    }
    //}
}
