﻿using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos.RuleEngineDomain;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IPhoneChargeService
    {
        Task<ApiResponse<object>> CreatePhoneChargeSettings(string userId, PhoneChargeDto model);
        Task<ApiResponse<PhoneServiceGetResponse>> GetPhoneChargeSettings(string userId, PhoneChargeType chargeType, int pageIndex, Applications? applications);
        Task<ApiResponse<object>> UpdatePhoneChargeSettings(string userId, UpdatePhoneChargeDto model);
        Task<ApiResponse<GetPhoneAmount>> GetPhoneNumberAmount(string userId, string countryId);
    }
}
