﻿using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Database.Entities
{
    public class ActivityLog : BaseEntity
    {
        public ActivityLog()
        {
            Id = Guid.NewGuid();
        }

        public Guid Id { get; set; }
        public ActivityActionType ActionType { get; set; }
        public Applications? Applications { get; set; }
        public string OldValue { get; set; }
        public string DescriptionOfActionPerformed { get; set; }
        public string AffectedObject { get; set; }
        public string PerformedByUserId { get; set; }
        public User PerformedByUser { get; set; }
    }
}
