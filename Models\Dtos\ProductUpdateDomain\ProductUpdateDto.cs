﻿namespace SuperAdmin.Service.Models.Dtos.ProductUpdateDomain
{
    public abstract class ProductUpdateDto
    {
        public Guid Id { get; set; }
        public string CategoryName { get; set; }
        public string? Package { get; set; }
        public string? Subject { get; set; }
        public string? Body { get; set; }
        public string? ImageAttachmentUrl { get; set; }
        public string FileName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ModifiedAt { get; set; }
    }

    public class SentProductUpdateDto : ProductUpdateDto
    {
        public double DateSentInMilliseconds { get; set; }
    }

    public class DraftProductUpdateDto : ProductUpdateDto
    {
        public double DateEditedInMilliseconds { get; set; }
    }

    public class DeletedProductUpdateDto : ProductUpdateDto
    {
        public double DateDeletedInMilliseconds { get; set; }
    }
}
