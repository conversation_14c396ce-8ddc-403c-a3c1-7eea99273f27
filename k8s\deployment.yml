apiVersion: apps/v1
kind: Deployment
metadata:
  name: superadmin-backend
  labels:
    app: superadmin-backend
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: superadmin-backend
  template:
    metadata:
      labels:
        app: superadmin-backend
    spec:
      containers:
        - name: superadmin-backend
          image: zarttechjobpro/jobpro:superAdV4.7
          imagePullPolicy: Always
          ports:      
            - containerPort: 80
      imagePullSecrets:
       - name: regcred


---
apiVersion: v1
kind: Service
metadata:
  name: superadmin-backend-service
spec:
  selector:
    app: superadmin-backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      port: 443
      targetPort: 80  
  type: NodePort
