﻿using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.RabbitMQ.MessagePayload;

namespace SuperAdmin.Service.Helpers
{
    public static class Mapper
    {
        public static ProductUpdateMessage MapToProductUpdatePayload(this ProductUpdate productUpdate)
        {
            return new ProductUpdateMessage
            (
            productUpdate.Id,
        productUpdate.Package,
       productUpdate.ProductUpdateCategory?.Name,
        productUpdate.Subject,
          productUpdate.Body,
       productUpdate.ImageUrl,
   productUpdate.PublishStatus
            );
        }
    }
}
