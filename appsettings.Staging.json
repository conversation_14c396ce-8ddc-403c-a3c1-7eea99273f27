{
  "AllowedHosts": "*",

  "ApplicationName": "jobpro",
  "ApplicationHostUrl": "https://sua.pactocoin.com",

  "ElasticConfiguration": {
    "Uri": "https://jobpro.es.us-central1.gcp.cloud.es.io",
    "CloudId": "Zarttech:dXMtY2VudHJhbDEuZ2NwLmNsb3VkLmVzLmlvOjQ0MyRlZmNlZWNjMmM5MjQ0ZGJiYWUzMGY3OWVlNTQ0MTZhZCQ2N2IxYzRhZjZjNTY0MjQwOGM1YjRiNjgzOGZhZDZiNA==",
    "Username": "elastic",
    "Password": "uGT1QlGNpJQ9GRuVcddQxJnN"
  },

  "RabbitMQ": {
    "HostName": "localhost",
    "Port": "5672"
  },

  "ConnectionStrings": {
    // "ConnectionStringEU": "Data Source=LOCALHOST\\SQLEXPRESS;Initial Catalog=SuperAdmin;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;MultipleActiveResultSets=True;"
    "ConnectionStringEU": "Data Source=*************;Database=SuperAdmin;User Id=superadmin;Password=****************************************;MultipleActiveResultSets=false;TrustServerCertificate=True"
    // "ConnectionStringEU": "Server=tcp:localhost,1433;Initial Catalog=JobProSuperAdmin;Persist Security Info=False;User ID=SA;Password=liquidKnight10!;MultipleActiveResultSets=False;Encrypt=False;TrustServerCertificate=False;Connection Timeout=60;"
  },

  "Serilog": {
    "MinimumLevel": {
      "Default": "Warning",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    }
  },

  "CorsSettings": {
    "AllowedOrigins": [
      "http://localhost:4200",
      "http://localhost:3000",
      "https://sua.pactocoin.com",
      "https://admin.pactocoin.com",
      "*"
    ]
  },

  "Services": {
    "Auth": "http://localhost:5095",
    "Monolith": "https://api.pactocoin.com"
  },

  "Jwt": {
    "Issuer": "app.jobpro.com",
    "Audience": "app.jobpro.com",
    "Key": "q<tK28od7!bkw#R-.#",
    "ExpiryInMinutes": 240,
    "Monolith": {
      "Issuer": "http://localhost",
      "Audience": "http://localhost",
      "Key": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
      "ExpiryInMinutes": 30
    }
  },

  "MAILGUN_API_KEY": "**************************************************",
  "MAILGUN_DOMAIN": "jobpro.app",

  "TwilioAccountSid": "**********************************",
  "TwilioApiSecret": "jbjjdQwwHZBjKPNOVhV8Qcv66UFg5DpC",
  "TwilioApiKey": "**********************************",
  "TwilioApiSID": "********************************",

  "WeavrConfigurations": {
    "API_KEY": "FkddGpWGZV0BjF5Xgk0ArQ==",
    //"BASE_URL": "https://localhost:5001/"
    //"BASE_URL": "https://wvr.jobpro.app/"
    "BASE_URL": "https://sandbox.weavr.io/multi/"
  },

  "GCSConfigOptions": {
    "BucketName": "jobpro-assets"
  },

  "WeavrEndpoints": {
    "CreateCorporateIdentity": "corporates",
    "SendEmailVerificationCodeForCorporates": "corporates/verification/email/send",
    "VerifyEmailForCorporates": "corporates/verification/email/verify",
    "CreatePassword": "passwords/{userid}/create",
    "LoginWithPassword": "login_with_password",
    "KYB": "corporates/kyb",
    "EnrolFor2FA": "authentication_factors/otp/{channel}",
    "VerifyCodeFor2FA": "authentication_factors/otp/{channel}/verify",
    "ManagedAccounts": "managed_accounts"
  },

  "WeavrProfiles": {
    "Corporates": "111567820722143405",
    "ManagedAccounts": "111567820778897581"
  },

  "RabbitMQConfiguration": {
    "Host": "amqps://pmbludyh:<EMAIL>/pmbludyh",
    "Port": "5672",
    "Username": "pmbludyh",
    "Password": "UgN4v14URvseEtIkAjyrolc0TpbWpr9W"
  }
}
