namespace SuperAdmin.Service.Models.Enums
{
    /// <summary>
    /// Status of currency rules
    /// </summary>
    public enum CurrencyRuleStatus
    {
        Active = 1,
        Inactive
    }

    /// <summary>
    /// Type of currency rule
    /// </summary>
    public enum CurrencyRuleType
    {
        SellRate = 1,
        CostAmount,
        ExternalSupplierCost
    }

    /// <summary>
    /// Business line for currency rules
    /// </summary>
    public enum BusinessLine
    {
        SaasSeats = 1,
        SophiaVoice,
        All
    }

    /// <summary>
    /// Currency codes supported by the system
    /// </summary>
    public enum CurrencyCode
    {
        USD = 1,
        EUR,
        GBP,
        NGN,
        NOK,
        CAD,
        AUD,
        JPY,
        CHF,
        SEK,
        DKK,
        PLN,
        CZK,
        HUF,
        RON,
        BGN,
        HRK,
        RSD,
        BAM,
        MKD,
        ALL,
        ISK,
        TRY,
        RUB,
        UAH,
        BYN,
        MDL,
        GEL,
        AMD,
        AZN,
        KZT,
        UZS,
        KGS,
        TJS,
        TMT
    }

    /// <summary>
    /// Version status for currency rule versions
    /// </summary>
    public enum CurrencyRuleVersionStatus
    {
        Active = 1,
        Inactive
    }

    /// <summary>
    /// External supplier types
    /// </summary>
    public enum ExternalSupplierType
    {
        Internal = 1,
        External
    }
}
