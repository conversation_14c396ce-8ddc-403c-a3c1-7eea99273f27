﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication;
using SuperAdmin.Service.Attributes;

namespace SuperAdmin.Service.Extensions
{
    public static class TokenRevocationMiddlewareExtensions
    {
        public static IApplicationBuilder UseTokenRevocationMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenRevocationMiddleware>();
        }
    }

    public class TokenRevocationMiddleware
    {
        private readonly RequestDelegate _next;

        public TokenRevocationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, TokenRevocationFilter tokenService)
        {
            // Check if the request is using the "monolith-schema" or the default scheme
            var result = await context.AuthenticateAsync("monolith-schema");

            var authScheme = result.Succeeded ? "monolith-schema" : JwtBearerDefaults.AuthenticationScheme;

            if (!authScheme.Contains("monolith-schema"))
            {
                var token = context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
                if (!string.IsNullOrEmpty(token) && await tokenService.IsTokenRevoked(token))
                {
                    var response = new
                    {
                        responseCode = "401",
                        responseMessage = "Token has been revoked.",
                        data = new
                        {
                            token = token,
                            refreshToken = string.Empty
                        }
                    };

                    context.Response.StatusCode = 401; // Unauthorized
                    context.Response.ContentType = "application/json";

                    var jsonResponse = System.Text.Json.JsonSerializer.Serialize(response);
                    await context.Response.WriteAsync(jsonResponse);
                    return;
                }
            }

            await _next(context);
        }
    }

}
