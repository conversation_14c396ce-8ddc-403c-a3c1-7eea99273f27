<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Configurations</name>
    </assembly>
    <members>
        <member name="M:Configurations.ConfigureLogsAndElasticSearch.ConfigureSerilog">
            <summary>
            This method is used to configure serilog
            </summary>
        </member>
        <member name="M:Configurations.ConfigureLogsAndElasticSearch.ConfigureElasticSearch(Microsoft.Extensions.Configuration.IConfigurationRoot,System.String)">
            <summary>
            Private method to configure elastic search
            </summary>
            <param name="config"></param>
            <param name="environment"></param>
            <returns></returns>
        </member>
        <member name="T:Configurations.Extensions.Extensions">
            <summary>
            this class is used to extend the functionality of the existing class
            </summary>
        </member>
        <member name="T:Configurations.Extensions.Middlewares.MiddlewareExtensions">
            <summary>
            
            </summary>
        </member>
        <member name="M:Configurations.Extensions.Middlewares.MiddlewareExtensions.InjectMiddlewares(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            This extension method is used to inject middlewares
            </summary>
            <param name="app"></param>
        </member>
        <member name="T:Configurations.Extensions.Services.CorsPolicyServiceExtension">
            <summary>
            
            </summary>
        </member>
        <member name="M:Configurations.Extensions.Services.CorsPolicyServiceExtension.AddCorsPolicyConfig(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            This extension method is used to inject cors policy
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:Configurations.Extensions.Services.HangfireServiceExtension">
            <summary>
            This class is used to inject hangfire configurations
            </summary>
        </member>
        <member name="M:Configurations.Extensions.Services.HangfireServiceExtension.AddHangfireConfig(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            This extension method is used to inject hangfire configurations
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:Configurations.Extensions.Services.ServiceExtensions">
            <summary>
            This extension class is used to inject services
            </summary>
        </member>
        <member name="M:Configurations.Extensions.Services.ServiceExtensions.InjectServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            This extension method is used to inject services
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:Configurations.Filters.HangfireAuthorizationFilter">
            <summary>
            This class is used to inject hangfire authorization filter
            </summary>
        </member>
        <member name="M:Configurations.Filters.HangfireAuthorizationFilter.AuthorizeAsync(Hangfire.Dashboard.DashboardContext)">
            <summary>
            This method is used to authorize acess hangfire dashboard
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Configurations.RabbitMQ.IRabbitMQMethods">
            <summary>
            This interface is used to get RabbitMQ channel
            </summary>
        </member>
        <member name="M:Configurations.RabbitMQ.IRabbitMQMethods.GetRabbitMQConnection">
            <summary>
            This method is used to get RabbitMQ channel
            </summary>
            <returns></returns>
        </member>
        <member name="T:Configurations.RabbitMQ.RabbitMQMethods">
            <summary>
            This class is used to get RabbitMQ channel
            </summary>
        </member>
        <member name="M:Configurations.RabbitMQ.RabbitMQMethods.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Contructor
            </summary>
            <param name="config"></param>
        </member>
        <member name="M:Configurations.RabbitMQ.RabbitMQMethods.GetRabbitMQConnection">
            <summary>
            this method is used to get RabbitMQ channel
            </summary>
            <returns></returns>
        </member>
        <member name="T:Configurations.Utility.PaginationParameters">
            <summary>
            This class is used to create the pagination parameters for the API.
            </summary>
        </member>
    </members>
</doc>
