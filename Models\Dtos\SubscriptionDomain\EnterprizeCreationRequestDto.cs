﻿using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.SubscriptionDomain
{
    public class EnterprizeCreationRequestDto
    {
        // Capitalize the first letter of each word in the company name value
        private string _companyName { get; set; } = default!;
        public string CompanyName
        {
            get => _companyName;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _companyName = value;
                }
                else
                {
                    _companyName = System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
                }
            }
        }

        private string _firstName { get; set; } = default!;
        public string FirstName
        {
            get => _firstName;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _firstName = value;
                }
                else
                {
                    _firstName = System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
                }
            }
        }

        private string _lastName { get; set; } = default!;
        public string LastName
        {
            get => _lastName;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _lastName = value;
                }
                else
                {
                    _lastName = System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(value.ToLower());
                }
            }
        }

        [System.ComponentModel.DataAnnotations.EmailAddress(ErrorMessage = "Invalid email address")]
        public string Email { get; set; } = default!;
        public string PhoneNumber { get; set; } = default!;
        public string CompanySize { get; set; } = default!;
        public Applications Application { get; set; } = Applications.Joble;
        public bool IsForContactUs { get; set; }
        public string? Message { get; set; }
        public string? Subject { get; set; }
    }
}
