﻿namespace SuperAdmin.Service.Attributes
{
    using Configurations.Utility;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Options;
    using Microsoft.IdentityModel.Tokens;
    using Serilog;
    using SuperAdmin.Service.Controllers;
    using SuperAdmin.Service.Database;
    using SuperAdmin.Service.Models.Configurations;
    using System.IdentityModel.Tokens.Jwt;
    using System.Security.Claims;
    using System.Text;
    using Twilio.Rest.Api.V2010.Account.Usage.Record;

    public class TokenRevocationFilter
    {
        private readonly AppDbContext _appDbContext;
        private readonly TokenValidationParameters _tokenValidationParameters;
        private readonly ILogger _logger = Log.ForContext<TokenRevocationFilter>();
        public TokenRevocationFilter(AppDbContext appDbContext,TokenValidationParameters tokenValidationParameters)
        {
            _appDbContext = appDbContext;
            _tokenValidationParameters = tokenValidationParameters;
        }

        public async Task<bool> IsTokenRevoked(string token)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(token))
                    return false;
                var security = new JwtSecurityTokenHandler();

                _tokenValidationParameters.ValidateLifetime = false;
                var tokenVerification = security.ValidateToken(token, _tokenValidationParameters, out var validatedToken);
                if (validatedToken is JwtSecurityToken jwtSecurityToken)
                {
                    var result = jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase);
                    if (!result)
                        return true;
                }

                // Retrieve the userid from the claim
                var userId = tokenVerification.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var isTokenRevoked = await _appDbContext.RefreshTokens.SingleOrDefaultAsync(x => x.UserId == userId && x.IsRevoked) != null;

                return isTokenRevoked;
            }
            catch (Exception ex)
            {
                _logger.Error($"An Error Occured. Message: {ex.Message} ||\n StackTrace: {ex.StackTrace} ||\n InnerException: {ex.InnerException}");
                return true;
            }
        }

        public async Task RevokeTokenAsync(string userId)
        {
            try
            {
                // Query for all tokens for the user that are not revoked
                var getTokens = _appDbContext.RefreshTokens
                    .Where(x => x.UserId == userId && !x.IsRevoked)
                    .ToList();

                if (getTokens.Any())
                {
                    // Revoke all the tokens
                    foreach (var item in getTokens)
                    {
                        item.IsRevoked = true;
                        _appDbContext.RefreshTokens.Update(item);
                    }

                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"An Error Occurred. Message: {ex.Message} ||\n StackTrace: {ex.StackTrace} ||\n InnerException: {ex.InnerException}");
            }
        }

    }
}


