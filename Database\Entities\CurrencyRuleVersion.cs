using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace SuperAdmin.Service.Database.Entities
{
    /// <summary>
    /// Represents a versioned snapshot of a currency rule, maintaining historical changes
    /// </summary>
    public class CurrencyRuleVersion : BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyRuleVersion"/> class
        /// </summary>
        public CurrencyRuleVersion()
        {
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Gets or sets the unique identifier for this currency rule version
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the parent currency rule this version belongs to
        /// </summary>
        public Guid CurrencyRuleId { get; set; }

        /// <summary>
        /// Gets or sets the application this currency rule version applies to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the sequential version number for this currency rule
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// Gets or sets the exchange rate for selling this currency to customers at this version
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the internal cost amount for this currency conversion at this version
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the type of external supplier used for this currency rule version
        /// </summary>
        public ExternalSupplierType ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the cost charged by the external supplier at this version
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the date when this version became effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the status of this currency rule version
        /// </summary>
        public CurrencyRuleVersionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who created this version
        /// </summary>
        public string CreatedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the parent currency rule this version belongs to
        /// </summary>
        [ForeignKey("CurrencyRuleId")]
        public virtual CurrencyRule CurrencyRule { get; set; }

        /// <summary>
        /// Gets or sets the user who created this version
        /// </summary>
        public virtual User CreatedByUser { get; set; }
    }
}
