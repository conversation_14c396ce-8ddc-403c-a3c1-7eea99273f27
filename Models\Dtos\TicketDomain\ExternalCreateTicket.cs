﻿using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Models.Dtos.TicketDomain
{
    public class ExternalCreateTicket
    {
        public string CustomerEmail { get; set; } = default!;

        public string CustomerName { get; set; } = default!;
        [Required]
        public Guid CategoryId { get; set; }

        [Required]
        public string Subject { get; set; } = default!;

        [Required]
        public string Message { get; set; } = default!;
    }
}
