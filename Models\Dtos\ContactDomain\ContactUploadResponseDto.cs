namespace SuperAdmin.Service.Models.Dtos.ContactDomain
{
    public class ContactUploadResponseDto
    {
        public int TotalContacts { get; set; }
        public int SuccessfulUploads { get; set; }
        public int FailedUploads { get; set; }
        public List<string> ErrorMessages { get; set; } = new List<string>();
        public List<ContactDto> UploadedContacts { get; set; } = new List<ContactDto>();
    }
}
