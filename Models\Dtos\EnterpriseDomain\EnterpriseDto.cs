﻿using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Models.Dtos.EnterpriseDomain
{
    public class EnterpriseDto
    {
        [Required]
        public Applications Application { get; set; }
        [Required]  
        public PaymentProviders Provider { get; set; }
        [Required]
        public double AmountPaid { get; set; }
        public string? PaymentId { get; set; }
        public DateTime? TransactionDate { get; set; }
        public string? Frequency { get; set; }
        public DateTime? ActivateOn { get; set; }
        public string? Currency { get; set; }
        public string? Logo { get; set; }
        public string? TransactionCode { get; set; }

        [Required]
        public string CompanyName { get; set; }
        [Required]
        public string CompanyType { get; set; }
        [Required]
        public string Subdomain { get; set; }
       // [Required]
        public string? AdminFirstname { get; set; }
       // [Required]
        public string? AdminLastname { get; set; }
        [Required]
        public string AdminEmail { get; set; }
        //[Required]
        public string? PersonalEmail { get; set; }
        [Required]
        public string PhoneNumber { get; set; }
        [Required]
        public string Country { get; set; }
        public int MaxNumberofUser { get; set; }
        public bool IsNumberOfUserUnlimited { get; set; }
        public int StorageInGigaByte { get; set; }
        public bool IsStorageUnlimited { get; set; }
        public int CommunicationHistoryInMonth { get; set; }
        public bool KeepCommunicationForever { get; set; }
        public int ProjectCreation { get; set; }
        public bool KeepProjectCreationForever { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        public bool KeepDataRetentionForever { get; set; }
        public bool AIAssistance { get; set; }
        public int CalenderLimit { get; set; }
        public bool UnlimitedActivityLog { get; set; }
        public bool TimeSheetManagement { get; set; }
        public IndustryType Industry { get; set; }
    }
}
