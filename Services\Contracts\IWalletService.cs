﻿using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.WalletDomain;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IWalletService
    {
        Task<ApiResponse<PaginationResult<WalletSummaryResponse>>> GetWalletAnalysis();
        Task<ApiResponse<Page<TransactionHistoryResponse>>> GetTransactionHistory(TransactionHistoryDto request, string? subdomain);
        Task<ApiResponse<GetCorperateWalletBalanceResponse>> GetCorperateWalletBalance(string? subdomain);

        Task<ApiResponse<List<GetAllWalletbalancesResponse>>> GetAllWalletbalances(string? subdomain);
        Task<ApiResponse<Page<TransactionHistoryResponse>>> GetFailedPayments(TransactionHistoryDto request, string? subdomain);
        Task<ApiResponse<GetTotalWalletBalanceResponse>> GetTotalWalletbalances(string? subdomain);
    }
}
