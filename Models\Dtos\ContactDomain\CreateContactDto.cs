using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Models.Dtos.ContactDomain
{
    public class CreateContactDto
    {
        [Required]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = default!;

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        public string? Email { get; set; }

        [Required]
        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string PhoneNumber { get; set; } = default!;

        public string? Industry { get; set; }

        [JsonIgnore]
        public string? Subdomain { get; set; }

    }
}
