using Configurations.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Security.Claims;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    //[Authorize]
    public class CurrencyRuleController : BaseController
    {
        private readonly ICurrencyRuleService _currencyRuleService;

        public CurrencyRuleController(ICurrencyRuleService currencyRuleService)
        {
            _currencyRuleService = currencyRuleService;
        }

        /// <summary>
        /// Get all currency rules with filtering and pagination
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of currency rules</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<CurrencyRuleDto>>), 200)]
        public async Task<IActionResult> GetCurrencyRules([FromQuery] CurrencyRuleFilterDto filter)
        {
            var response = await _currencyRuleService.GetCurrencyRules(filter);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get a specific currency rule by ID
        /// </summary>
        /// <param name="id">Currency rule ID</param>
        /// <returns>Currency rule details</returns>
        [HttpGet("get/{id}")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 404)]
        public async Task<IActionResult> GetCurrencyRuleById(Guid id)
        {
            var response = await _currencyRuleService.GetCurrencyRuleById(id);
            return ParseResponse(response);
        }

        /// <summary>
        /// Create a new currency rule
        /// </summary>
        /// <param name="model">Currency rule creation model</param>
        /// <returns>Created currency rule</returns>
        [HttpPost("create")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 400)]
        public async Task<IActionResult> CreateCurrencyRule([FromBody] CreateCurrencyRuleDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.CreateCurrencyRule(model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Update an existing currency rule
        /// </summary>
        /// <param name="id">Currency rule ID</param>
        /// <param name="model">Currency rule update model</param>
        /// <returns>Updated currency rule</returns>
        [HttpPut("update/{id}")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleDto>), 404)]
        public async Task<IActionResult> UpdateCurrencyRule(Guid id, [FromBody] UpdateCurrencyRuleDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.UpdateCurrencyRule(id, model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Delete a currency rule
        /// </summary>
        /// <param name="id">Currency rule ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("delete/{id}")]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 404)]
        public async Task<IActionResult> DeleteCurrencyRule(Guid id)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.DeleteCurrencyRule(id, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Activate or deactivate a currency rule
        /// </summary>
        /// <param name="id">Currency rule ID</param>
        /// <param name="isActive">Activation status</param>
        /// <returns>Success status</returns>
        [HttpPatch("activate-deactivate/{id}")]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 404)]
        public async Task<IActionResult> ActivateDeactivateCurrencyRule(Guid id, [FromQuery] bool isActive)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.ActivateDeactivateCurrencyRule(id, isActive, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get version history for a currency rule
        /// </summary>
        /// <param name="id">Currency rule ID</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated list of currency rule versions</returns>
        [HttpGet("get-versions{id}")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<CurrencyRuleVersionDto>>), 200)]
        public async Task<IActionResult> GetCurrencyRuleVersions(Guid id, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            var response = await _currencyRuleService.GetCurrencyRuleVersions(id, page, pageSize);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get a specific currency rule version
        /// </summary>
        /// <param name="versionId">Version ID</param>
        /// <returns>Currency rule version details</returns>
        [HttpGet("versions/{versionId}")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleVersionDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyRuleVersionDto>), 404)]
        public async Task<IActionResult> GetCurrencyRuleVersionById(Guid versionId)
        {
            var response = await _currencyRuleService.GetCurrencyRuleVersionById(versionId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get currency conversion rates with filtering and pagination
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of currency conversion rates</returns>
        [HttpGet("get-conversion-rates")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<CurrencyConversionRateDto>>), 200)]
        public async Task<IActionResult> GetCurrencyConversionRates([FromQuery] CurrencyConversionRateFilterDto filter)
        {
            var response = await _currencyRuleService.GetCurrencyConversionRates(filter);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get a specific currency conversion rate by ID
        /// </summary>
        /// <param name="id">Conversion rate ID</param>
        /// <returns>Currency conversion rate details</returns>
        [HttpGet("get-conversion-rates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 404)]
        public async Task<IActionResult> GetCurrencyConversionRateById(Guid id)
        {
            var response = await _currencyRuleService.GetCurrencyConversionRateById(id);
            return ParseResponse(response);
        }

        /// <summary>
        /// Create a new currency conversion rate
        /// </summary>
        /// <param name="model">Currency conversion rate creation model</param>
        /// <returns>Created currency conversion rate</returns>
        [HttpPost("create-conversion-rates")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 400)]
        public async Task<IActionResult> CreateCurrencyConversionRate([FromBody] CreateCurrencyConversionRateDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.CreateCurrencyConversionRate(model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Update an existing currency conversion rate
        /// </summary>
        /// <param name="id">Conversion rate ID</param>
        /// <param name="model">Currency conversion rate update model</param>
        /// <returns>Updated currency conversion rate</returns>
        [HttpPut("update-conversion-rates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<CurrencyConversionRateDto>), 404)]
        public async Task<IActionResult> UpdateCurrencyConversionRate(Guid id, [FromBody] UpdateCurrencyConversionRateDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.UpdateCurrencyConversionRate(id, model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Delete a currency conversion rate
        /// </summary>
        /// <param name="id">Conversion rate ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("delete-conversion-rates/{id}")]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 200)]
        [ProducesResponseType(typeof(ApiResponse<BooleanResponse>), 404)]
        public async Task<IActionResult> DeleteCurrencyConversionRate(Guid id)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.DeleteCurrencyConversionRate(id, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Bulk import currency rules from CSV
        /// </summary>
        /// <param name="model">Bulk import model</param>
        /// <returns>Import result</returns>
        [HttpPost("bulk-import")]
        [ProducesResponseType(typeof(ApiResponse<BulkImportResultDto>), 200)]
        public async Task<IActionResult> BulkImportCurrencyRules([FromBody] BulkCurrencyRuleImportDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.BulkImportCurrencyRules(model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Bulk import currency conversion rates from CSV
        /// </summary>
        /// <param name="model">Bulk import model</param>
        /// <returns>Import result</returns>
        [HttpPost("conversion-rates/bulk-import")]
        [ProducesResponseType(typeof(ApiResponse<BulkImportResultDto>), 200)]
        public async Task<IActionResult> BulkImportCurrencyConversionRates([FromBody] BulkCurrencyRateImportDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var response = await _currencyRuleService.BulkImportCurrencyConversionRates(model, userId);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get audit trail for currency rules
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of audit logs</returns>
        [HttpGet("audit-logs")]
        [ProducesResponseType(typeof(ApiResponse<PaginationResult<CurrencyRuleAuditLogDto>>), 200)]
        public async Task<IActionResult> GetCurrencyRuleAuditLogs([FromQuery] CurrencyRuleAuditFilterDto filter)
        {
            var response = await _currencyRuleService.GetCurrencyRuleAuditLogs(filter);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get conversion rate between two currencies
        /// </summary>
        /// <param name="fromCurrency">Source currency</param>
        /// <param name="toCurrency">Target currency</param>
        /// <param name="effectiveDate">Effective date (optional)</param>
        /// <param name="application">Application filter (optional)</param>
        /// <returns>Conversion rate</returns>
        [HttpGet("get-conversion-rate-cur")]
        [ProducesResponseType(typeof(ApiResponse<DecimalResponse>), 200)]
        public async Task<IActionResult> GetConversionRate([FromQuery] string fromCurrency, [FromQuery] string toCurrency, [FromQuery] DateTime? effectiveDate = null, [FromQuery] Applications? application = null)
        {
            var response = await _currencyRuleService.GetConversionRate(fromCurrency, toCurrency, effectiveDate, application);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get list of supported currencies
        /// </summary>
        /// <returns>List of supported currencies</returns>
        [HttpGet("supported-currencies")]
        [ProducesResponseType(typeof(ApiResponse<StringListResponse>), 200)]
        public async Task<IActionResult> GetSupportedCurrencies()
        {
            var response = await _currencyRuleService.GetSupportedCurrencies();
            return ParseResponse(response);
        }

        /// <summary>
        /// Get list of business lines
        /// </summary>
        /// <returns>List of business lines</returns>
        [HttpGet("business-lines")]
        [ProducesResponseType(typeof(ApiResponse<StringListResponse>), 200)]
        public async Task<IActionResult> GetBusinessLines()
        {
            var response = await _currencyRuleService.GetBusinessLines();
            return ParseResponse(response);
        }
    }
}
