using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain
{
    public class CurrencyRuleAuditLogDto
    {
        public Guid Id { get; set; }
        public Guid CurrencyRuleId { get; set; }
        public string Application { get; set; }
        public string ActionType { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public string FieldChanged { get; set; }
        public string Reason { get; set; }
        public string PerformedBy { get; set; }
        public DateTime Timestamp { get; set; }
        public string Date { get; set; }
        public string Time { get; set; }
    }

    public class CurrencyRuleAuditFilterDto
    {
        public Applications? Application { get; set; }
        public Guid? CurrencyRuleId { get; set; }
        public ActivityActionType? ActionType { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string? PerformedBy { get; set; }
        public string? FieldChanged { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class BulkCurrencyRuleImportDto
    {
        public List<CurrencyRuleImportItem> Rules { get; set; } = new List<CurrencyRuleImportItem>();
    }

    public class CurrencyRuleImportItem
    {
        public string BusinessLine { get; set; }
        public string SellCurrency { get; set; }
        public string CostCurrency { get; set; }
        public decimal SellRate { get; set; }
        public decimal CostAmount { get; set; }
        public string ExternalSupplier { get; set; }
        public decimal ExternalSupplierCost { get; set; }
        public DateTime EffectiveDate { get; set; }
    }
}
