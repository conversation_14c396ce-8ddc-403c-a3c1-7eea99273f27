﻿using Configurations.Utility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos.ActivityLog;
using SuperAdmin.Service.Models.Dtos.WalletDomain;
using SuperAdmin.Service.Services.Contracts;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class WalletController : BaseController
    {
        private readonly IWalletService _walletService;

        public WalletController(IWalletService walletService)
        {
            _walletService = walletService;
        }

        /// <summary>
        /// Get wallet 
        /// </summary>       
        /// <returns></returns>
        [HttpGet()]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<List<ActivityLogResponse>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<ActivityLogResponse>>), 404)]
        public async Task<IActionResult> GetWallet()
        {
            var response = await _walletService.GetWalletAnalysis();
            return ParseResponse(response);
        }

        /// <summary>
        /// This method is used to get transacttion history
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet("transaction/history")]
        [ProducesResponseType(typeof(ApiResponse<Page<TransactionHistoryResponse>>), 200)]
        public async Task<IActionResult> GetTransactionHistory([FromQuery] TransactionHistoryDto model)
        {
            // Extract subdomain from HTTP request headers
            var subdomain = HttpContext.Request.Headers["subdomain"];

            var response = await _walletService.GetTransactionHistory(model, subdomain);
            return ParseResponse(response);
        }

        /// <summary>
        /// This method is used to get corporate walle balance
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet("corporate/balance")]
        [ProducesResponseType(typeof(ApiResponse<GetCorperateWalletBalanceResponse>), 200)]
        public async Task<IActionResult> GetCorporateWalletBalance()
        {
            // Extract subdomain from HTTP request headers
            var subdomain = HttpContext.Request.Headers["subdomain"];

            var response = await _walletService.GetCorperateWalletBalance(subdomain);
            return ParseResponse(response);
        }




        /// <summary>
        /// This method is used to get all balances
        /// </summary>
        /// <returns></returns>
        [HttpGet("AllWallet/balanceHistory")]
        [ProducesResponseType(typeof(ApiResponse<List<GetAllWalletbalancesResponse>>), 200)]
        public async Task<IActionResult> GetAllWalletbalances()
        {
            // Extract subdomain from HTTP request headers
            var subdomain = HttpContext.Request.Headers["subdomain"];

            var response = await _walletService.GetAllWalletbalances(subdomain);
            return ParseResponse(response);
        }


        /// <summary>
        /// This method is used to get transacttion history
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet("failedpayment/history")]
        [ProducesResponseType(typeof(ApiResponse<Page<TransactionHistoryResponse>>), 200)]
        public async Task<IActionResult> GetFailedPayments([FromQuery] TransactionHistoryDto model)
        {
            // Extract subdomain from HTTP request headers
            var subdomain = HttpContext.Request.Headers["subdomain"];

            var response = await _walletService.GetTransactionHistory(model, subdomain);
            return ParseResponse(response);
        }


        /// <summary>
        /// This method is used to get corporate walle balance
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpGet("total/balance")]
        [ProducesResponseType(typeof(ApiResponse<GetTotalWalletBalanceResponse>), 200)]
        public async Task<IActionResult> GetTotalWalletAvailableBalance()
        {
            // Extract subdomain from HTTP request headers
            var subdomain = HttpContext.Request.Headers["subdomain"];

            var response = await _walletService.GetTotalWalletbalances(subdomain);
            return ParseResponse(response);
        }



    }
}
