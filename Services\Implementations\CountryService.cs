﻿using System.Text.Json;
using Configurations.Utility;
using Serilog;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Models.Dtos.CountryDomain;
using SuperAdmin.Service.Services.Contracts;
using ILogger = Serilog.ILogger;

namespace SuperAdmin.Service.Services.Implementations
{
    public class CountryService : ICountryService
    {
        private readonly AppDbContext _appDbContext;
        private readonly ILogger _logger = Log.ForContext<HttpProjectService>();
        private readonly HttpClient _httpClient;

        public CountryService(AppDbContext appDbContext, IConfiguration configuration)
        {
            _appDbContext = appDbContext;
            var baseAddress = configuration.GetSection("Services")?.GetSection("Monolith")?.Value;
            if (baseAddress == null)
                throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

            _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };
        }

        public async Task<ApiResponse<List<TenantCountryPercentage>>> GetAllCountriesPercentage(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
                var response = await _httpClient.GetAsync("/api/tenant/countriespercentage");
                var result = await HandleApiResponse<List<TenantCountryPercentage>>(response);

                return result;
            }
            catch (HttpRequestException httpEx)
            {
                _logger.Error($"Error in GetAllCountriesPercentage: {httpEx.Message}");
                return new ApiResponse<List<TenantCountryPercentage>>
                {
                    ResponseCode = httpEx.StatusCode.ToString(),
                    ResponseMessage = "Internal Server Error"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetAllCountriesPercentage: {ex.Message}");
                return new ApiResponse<List<TenantCountryPercentage>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        /// <summary>
        /// This method gets countries
        /// </summary>
        /// <param name="country"></param>
        /// <returns></returns>
        public async Task<ApiResponse<IEnumerable<CountryDto>>> GetCountries(string? country)
        {
            IQueryable<CountryDto> records;

            if (!string.IsNullOrWhiteSpace(country))
            {
                records = _appDbContext.Country.Where(x => x.Name.Contains(country)).Select(x => new CountryDto
                {
                    Id = x.Id,
                    Country = x.Name,
                    CountryCode = x.TwoLetterCode
                }).OrderBy(x => x.Country);
            }
            else
            {
                records = _appDbContext.Country.Select(x => new CountryDto
                {
                    Id = x.Id,
                    Country = x.Name,
                    CountryCode = x.TwoLetterCode
                }).OrderBy(x => x.Country);
            }

            return new ApiResponse<IEnumerable<CountryDto>>
            {
                Data = records,
                ResponseCode = "200"
            };
        }

        public async Task<ApiResponse<List<TenantCountryDto>>> TopPerformingCountries(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);
                var response = await _httpClient.GetAsync("/api/tenant/gettopperformingcountries");
                var result = await HandleApiResponse<List<TenantCountryDto>>(response);

                return result;
            }
            catch (HttpRequestException httpEx)
            {
                _logger.Error($"Error in TopPerformingCountries: {httpEx.Message}");
                return new ApiResponse<List<TenantCountryDto>>
                {
                    ResponseCode = httpEx.StatusCode.ToString(),
                    ResponseMessage = "Internal Server Error"
                };
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in TopPerformingCountries: {ex.Message}");
                return new ApiResponse<List<TenantCountryDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        private async Task<ApiResponse<T>> HandleApiResponse<T>(HttpResponseMessage response)
        {
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<T?>>(responseBody);

                return new ApiResponse<T>
                {
                    Data = result.Data,
                    ResponseCode = result?.ResponseCode,
                    ResponseMessage = result?.ResponseMessage
                };
            }
            else
            {
                var statusCode = (int)response.StatusCode;
                var errorMessage = await response.Content.ReadAsStringAsync();

                var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<T>>(errorMessage)
                                       ?? new ApiResponse<T>
                                       {
                                           ResponseCode = statusCode.ToString(),
                                           ResponseMessage = "Failed to fetch data"
                                       };

                _logger.Error($"Error in HandleApiResponse: {errorMessage}");

                return errorApiResponse;
            }
        }
    }
}
