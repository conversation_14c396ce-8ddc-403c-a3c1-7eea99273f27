﻿using Configurations.Utility;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.RoleDomain;
using SuperAdmin.Service.Models.Dtos.UserDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Data;

namespace SuperAdmin.Service.Services.Implementations
{
    public class RoleService : IRoleService
    {
        private readonly RoleManager<Role> _roleManager;
        private readonly AppDbContext _appDbContext;
        private readonly UserManager<User> _userManager;
        public RoleService(RoleManager<Role> roleManager, AppDbContext appDbContext, UserManager<User> userManager)
        {
            _roleManager = roleManager;
            _appDbContext = appDbContext;
            _userManager = userManager;
        }

        /// <summary>
        /// This method creates a new role and assigns permissions 
        /// </summary>
        /// <param name="model"></param>
        public async Task<ApiResponse<RoleDto>> CreateRole(CreateRole model)
        {
            if (string.IsNullOrWhiteSpace(model.RoleName) || model.PermissionIds == null || model.PermissionIds.Length == 0)
            {
                return new ApiResponse<RoleDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide required parameters"
                };
            }

            var isRoleDeleted = await _roleManager.Roles.FirstOrDefaultAsync(x => x.Name.ToLower() == model.RoleName.ToLower() & x.IsDeleted);
            if (isRoleDeleted != null)
            {
                var permission = _appDbContext.Permissions.Where(x => model.PermissionIds.Contains(x.Id));
                if (!permission.Any())
                {
                    return new ApiResponse<RoleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Role cannot be created without an assigned permission."
                    };
                }

                isRoleDeleted.IsDeleted = false;
                var results = await _roleManager.UpdateAsync(isRoleDeleted);
                if (!results.Succeeded)
                {
                    return new ApiResponse<RoleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = string.Join(".", results.Errors.Select(x => x.Description))
                    };
                }

                var getRolePermission = _appDbContext.RolePermissions.Where(x => x.RoleId == isRoleDeleted.Id).ToList();
                _appDbContext.RolePermissions.RemoveRange(getRolePermission);

                foreach (var item in model.PermissionIds)
                {
                    _appDbContext.RolePermissions.Add(new RolePermission
                    {
                        PermissionId = item,
                        RoleId = isRoleDeleted.Id
                    });
                }
                await _appDbContext.SaveChangesAsync();
                return new ApiResponse<RoleDto>
                {
                    Data = new RoleDto
                    {
                        Id = isRoleDeleted.Id,
                        Name = isRoleDeleted.Name,
                        DateCreatedInEpochMilliseconds = isRoleDeleted.CreatedAt.ToEpochTimestampInMilliseconds()
                    },
                    ResponseCode = "200",
                    ResponseMessage = $"Created {isRoleDeleted.Name} role successfully"
                };
            }
            else
            {
                bool roleExists = await _roleManager.Roles.FirstOrDefaultAsync(x => x.Name.ToLower() == model.RoleName.ToLower() & !x.IsDeleted) != null;
                if (roleExists)
                {
                    return new ApiResponse<RoleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = $"Role name '{model.RoleName}' already exists"
                    };
                }

                var permissions = _appDbContext.Permissions.Where(x => model.PermissionIds.Contains(x.Id));
                if (!permissions.Any())
                {
                    return new ApiResponse<RoleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Role cannot be created without an assigned permission."
                    };
                }

                Role role = new Role
                {
                    ConcurrencyStamp = Guid.NewGuid().ToString(),
                    Name = model.RoleName,
                    NormalizedName = model.RoleName.ToUpper(),
                };

                var result = await _roleManager.CreateAsync(role);
                if (!result.Succeeded)
                {
                    return new ApiResponse<RoleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = string.Join(".", result.Errors.Select(x => x.Description))
                    };
                }

                foreach (var permission in permissions)
                {
                    _appDbContext.RolePermissions.Add(new RolePermission
                    {
                        PermissionId = permission.Id,
                        RoleId = role.Id
                    });
                }

                await _appDbContext.SaveChangesAsync();
                return new ApiResponse<RoleDto>
                {
                    Data = new RoleDto
                    {
                        Id = role.Id,
                        Name = role.Name,
                        DateCreatedInEpochMilliseconds = role.CreatedAt.ToEpochTimestampInMilliseconds()
                    },
                    ResponseCode = "200",
                    ResponseMessage = $"Created {role.Name} role successfully"
                };
            }

        }

        /// <summary>
        /// This method deletes a role
        /// </summary>
        /// <param name="id"></param>
        public async Task<ApiResponse<dynamic>> DeleteRole(string id)
        {
            var role = await _appDbContext.Roles.FindAsync(id);

            if (role == null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Role does not exist"
                };
            }

            if (role.Name == HelperConstants.SUPER_ADMIN_ROLE || role.Name == HelperConstants.SUSPENSION_ROLE)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "409",
                    ResponseMessage = $"{role.Name} cannot be deleted"
                };
            }

            role.IsDeleted = true;
            var result = await _roleManager.UpdateAsync(role);
            if (!result.Succeeded)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = string.Join(".", result.Errors.Select(x => x.Description))
                };
            }

            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = "Deleted role successfully"
            };
        }

        public async Task<ApiResponse<dynamic>> EditRole(EditRoleDto dto)
        {
            var role = await _appDbContext.Roles.FindAsync(dto.RoleId);

            if (role == null)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Role does not exist"
                };
            }

            if (role.Name == HelperConstants.SUPER_ADMIN_ROLE || role.Name == HelperConstants.SUSPENSION_ROLE)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "409",
                    ResponseMessage = $"{role.Name} cannot be edited"
                };
            }

            var permission = _appDbContext.Permissions.Where(x => dto.PermissionIds.Contains(x.Id));
            if (!permission.Any())
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Role cannot be created without an assigned permission."
                };
            }
            var getRolePermission = _appDbContext.RolePermissions.Where(x => x.RoleId == role.Id).ToList();
            _appDbContext.RolePermissions.RemoveRange(getRolePermission);

            foreach (var item in dto.PermissionIds)
            {
                _appDbContext.RolePermissions.Add(new RolePermission
                {
                    PermissionId = item,
                    RoleId = role.Id
                });
            }

            await _appDbContext.SaveChangesAsync();
            return new ApiResponse<dynamic>
            {
                ResponseCode = "200",
                ResponseMessage = $"Edited {role.Name} role successfully"
            };
        }

        /// <summary>
        /// This method gets all roles with filtering, ordering, and pagination
        /// </summary>
        /// <param name="filter">Filtering, ordering, and pagination options</param>
        /// <returns></returns>
        public async Task<ApiResponse<PaginationResult<RoleDto>>> GetRoles(FilterRoleBy filter)
        {
            var rolesQuery = _appDbContext.Roles
                                          .Where(x => x.Name != HelperConstants.SUSPENSION_ROLE && !x.IsDeleted);

            // Apply filtering by role name
            if (!string.IsNullOrEmpty(filter.RoleName))
            {
                rolesQuery = rolesQuery.Where(x => x.Name!.Contains(filter.RoleName));
            }

            // Apply ordering based on the OrderBy enum
            if (filter.OrderBy == OrderBy.RecentlyAdded)
            {
                rolesQuery = rolesQuery.OrderByDescending(x => x.CreatedAt);
            }
            else if (filter.OrderBy == OrderBy.Oldest)
            {
                rolesQuery = rolesQuery.OrderBy(x => x.CreatedAt);
            }

            // Project to RoleDto and calculate the number of users for each role
            var projectedRolesQuery = rolesQuery.Select(x => new RoleDto
            {
                Id = x.Id,
                Name = x.Name!,
                NumberOfUsers = _appDbContext.UserRoles.Count(ur => ur.RoleId == x.Id &&
                                                                    _userManager.Users.Any(u => u.Id == ur.UserId && !u.IsDeleted && !u.IsSuspended)),
                Users = _appDbContext.UserRoles
                             .Where(ur => ur.RoleId == x.Id &&
                                          _userManager.Users.Any(u => u.Id == ur.UserId && !u.IsDeleted && !u.IsSuspended))
                             .Join(_userManager.Users.Include(x => x.Country),
                                   ur => ur.UserId,
                                   u => u.Id,
                                   (ur, user) => new UserDto
                                   {
                                       UserId = user.Id,
                                       FirstName = user.FirstName,
                                       LastName = user.LastName,
                                       Email = user.Email!,
                                       Role = x.Name!,
                                       RoleId = x.Id.ToString(),
                                       CreatedAt = user.CreatedAt,
                                       DateCreatedInEpochMilliseconds = user.CreatedAt.ToEpochTimestampInMilliseconds(),
                                       Phone = user.PhoneNumber!,
                                       Country = user.Country.Name
                                   }).ToList(),
                DateCreatedInEpochMilliseconds = x.CreatedAt.ToEpochTimestampInMilliseconds()
            });

            // Apply pagination
            var paginatedRoles = await PaginationHelper.PaginateRecords(projectedRolesQuery, filter.Page ?? 1, filter.PageSize ?? 30);

            // Return the paginated result
            return new ApiResponse<PaginationResult<RoleDto>>
            {
                Data = paginatedRoles,
                ResponseCode = "200"
            };
        }



        /// <summary>
        /// This method gets permissions assigned to a role
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public async Task<ApiResponse<RolePermissionDto>> GetPermissionsForARole(string roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId);
            if (role is null || role.IsDeleted)
            {
                return new ApiResponse<RolePermissionDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Role not found"
                };
            }

            var permissions = await _appDbContext.RolePermissions.Where(x => x.RoleId == roleId).Include(x => x.Permission)
                                                                                    .Select(x => x.Permission.Description).ToArrayAsync();

            var rolePermissions = new RolePermissionDto
            {
                RoleName = role.Name!,
                Permissions = permissions
            };

            return new ApiResponse<RolePermissionDto>
            {
                ResponseCode = "200",
                Data = rolePermissions
            };
        }


    }
}
