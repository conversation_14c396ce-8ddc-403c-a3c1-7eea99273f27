using System.Text.Json;
using Configurations.Utility;
using Serilog;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using ILogger = Serilog.ILogger;

namespace SuperAdmin.Service.Services.Implementations;
public class ActivityService : IActivityService, IDisposable
{
    private readonly ILogger _logger = Log.ForContext<HttpProjectService>();
    private readonly HttpClient _httpClient;

    public ActivityService(IConfiguration configuration)
    {
        var baseAddress = configuration.GetSection("Services")?.GetSection("Monolith")?.Value;
        if (baseAddress == null)
            throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

        _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing) => _httpClient.Dispose();

    public async Task<ApiResponse<List<MonthlyActivityCount>>> GetActivitiesStatistics(string? subdomain, Applications application)
    {
        try
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

            var response = await _httpClient.GetAsync("/api/activity/tenant/getallactivities?applications=" + application);
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<List<MonthlyActivityCount>>>(responseBody);

                return new ApiResponse<List<MonthlyActivityCount>>
                {
                    Data = result?.Data,
                    ResponseCode = result?.ResponseCode,
                    ResponseMessage = result?.ResponseMessage
                };
            }
            else
            {
                var statusCode = (int)response.StatusCode;
                var errorMessage = await response.Content.ReadAsStringAsync();

                var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<List<MonthlyActivityCount>>>(errorMessage)
                                       ?? new ApiResponse<List<MonthlyActivityCount>>
                                       {
                                           ResponseCode = statusCode.ToString(),
                                           ResponseMessage = "Failed to fetch data"
                                       };
                _logger.Error($"Error in GetActivitiesStatistics: {errorMessage}");

                return errorApiResponse;
            }
        }
        catch (HttpRequestException httpEx)
        {
            _logger.Error($"Error in GetActivitiesStatistics: {httpEx.Message}");
            return new ApiResponse<List<MonthlyActivityCount>>
            {
                ResponseCode = httpEx.StatusCode.ToString(),
                ResponseMessage = "Internal Server Error"
            };
        }
        catch (Exception ex)
        {
            _logger.Error($"Error in GetActivitiesStatistics: {ex.Message}");
            return new ApiResponse<List<MonthlyActivityCount>>
            {
                ResponseCode = "500",
                ResponseMessage = "Failed to fetch data"
            };
        }
    }

    public async Task<ApiResponse<Page<ActivityCountPerTenant>>> GetTenantsCounts(string? subdomain, ActivityQueryParameters queryParameters)
    {
        try
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

            var requestUri = $"/api/activity/tenant/getactivities?" +
                                $"fromDate={queryParameters.FromDate}&" +
                                             $"pageNumber={queryParameters.Page}&pageSize={queryParameters.PageSize}&" +
                                             $"companyName={queryParameters.CompanyName}&" +
                                             $"application={queryParameters.Application}&toDate={queryParameters.ToDate}";

            var response = await _httpClient.GetAsync(requestUri);
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<Page<ActivityCountPerTenant>>>(responseBody);

                return new ApiResponse<Page<ActivityCountPerTenant>>
                {
                    Data = result?.Data,
                    ResponseCode = result?.ResponseCode,
                    ResponseMessage = result?.ResponseMessage
                };
            }
            else
            {
                var statusCode = (int)response.StatusCode;
                var errorMessage = await response.Content.ReadAsStringAsync();

                var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<Page<ActivityCountPerTenant>>>(errorMessage)
                                       ?? new ApiResponse<Page<ActivityCountPerTenant>>
                                       {
                                           ResponseCode = statusCode.ToString(),
                                           ResponseMessage = "Failed to fetch data"
                                       };
                _logger.Error($"Error in GetActivitiesStatistics: {errorMessage}");
                return errorApiResponse;
            }
        }
        catch (HttpRequestException httpEx)
        {
            _logger.Error($"Error in GetActivitiesStatistics: {httpEx.Message}");
            return new ApiResponse<Page<ActivityCountPerTenant>>
            {
                ResponseCode = httpEx.StatusCode.ToString(),
                ResponseMessage = "Internal Server Error"
            };
        }
        catch (Exception ex)
        {
            _logger.Error($"Error in GetActivitiesStatistics: {ex.Message}");
            return new ApiResponse<Page<ActivityCountPerTenant>>
            {
                ResponseCode = "500",
                ResponseMessage = "Failed to fetch data"
            };
        }
    }

}