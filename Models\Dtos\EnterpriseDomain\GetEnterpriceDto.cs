﻿namespace SuperAdmin.Service.Models.Dtos.EnterpriseDomain
{
    public class GetEnterpriceDto
    {
        public string ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string DevResponseMessage { get; set; }
        public List<EnterpriceData> Data { get; set; }

    }
    public class EnterpriceData
    {
        public string Application { get; set; }
        public string EnterpriseSubId { get; set; }
        public string SubscriptionId { get; set; }
        public string TenantId { get; set; }
        public string CompanyName { get; set; }
        public DateTime DateRegistered { get; set; }
        public string Location { get; set; }
        public string AdminEmail { get; set; }
        public string CompanyType { get; set; }
        public int MaxNumberofUser { get; set; }
        public bool IsNumberOfUserUnlimited { get; set; }
        public int StorageInGigaByte { get; set; }
        public bool IsStorageUnlimited { get; set; }
        public int CommunicationHistoryInMonth { get; set; }
        public bool KeepCommunicationForever { get; set; }
        public int ProjectCreation { get; set; }
        public bool KeepProjectCreationForever { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        public bool KeepDataRetentionForever { get; set; }
        public bool AIAssistance { get; set; }
        public bool UnlimitedActivityLog { get; set; }
        public bool TimeSheetManagement { get; set; }
        public string Status { get; set; }

    }
}
