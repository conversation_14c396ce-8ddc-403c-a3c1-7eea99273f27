﻿using Jobid;
using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Models.Dtos.WalletDomain
{
    public class TransactionHistoryDto : Pagination
    {
        [Required]
        public Applications Application { get; set; }
        public PaymentProviders Provider { get; set; }
        public string? PlanId { get; set; }
        public string? CompanyName { get; set; }
        public TransactionHistorySortBy? SortBy { get; set; }
        [DateRangeValidation]
        public DateTime? FromDate { get; set; }
        [DateRangeValidation]
        public DateTime? ToDate { get; set; }
    }

}
