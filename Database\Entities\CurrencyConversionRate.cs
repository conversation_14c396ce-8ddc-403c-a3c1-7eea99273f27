using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace SuperAdmin.Service.Database.Entities
{
    /// <summary>
    /// Represents a currency conversion rate between two currencies for a specific application
    /// </summary>
    public class CurrencyConversionRate : BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyConversionRate"/> class
        /// </summary>
        public CurrencyConversionRate()
        {
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Gets or sets the unique identifier for this currency conversion rate
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the application this conversion rate applies to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the source currency being converted from
        /// </summary>
        public CurrencyCode FromCurrency { get; set; }

        /// <summary>
        /// Gets or sets the target currency being converted to
        /// </summary>
        public CurrencyCode ToCurrency { get; set; }

        /// <summary>
        /// Gets or sets the exchange rate for converting from the source to target currency
        /// </summary>
        [Column(TypeName = "decimal(18,6)")]
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Gets or sets the date when this conversion rate becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the optional expiry date for this conversion rate
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this conversion rate is currently active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the source of this conversion rate (e.g., "Manual", "API", "Bank")
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who created this conversion rate
        /// </summary>
        public string CreatedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who last modified this conversion rate
        /// </summary>
        public string? ModifiedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the user who created this conversion rate
        /// </summary>
        public virtual User CreatedByUser { get; set; }

        /// <summary>
        /// Gets or sets the user who last modified this conversion rate
        /// </summary>
        public virtual User? ModifiedByUser { get; set; }
    }
}
