﻿using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.RoleDomain;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IRoleService
    {
        Task<ApiResponse<PaginationResult<RoleDto>>> GetRoles(FilterRoleBy filter);
        Task<ApiResponse<RoleDto>> CreateRole(CreateRole model);
        Task<ApiResponse<dynamic>> DeleteRole(string id);
        Task<ApiResponse<RolePermissionDto>> GetPermissionsForARole(string roleId);
        Task<ApiResponse<dynamic>> EditRole(EditRoleDto dto);
    }
}
