namespace SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain
{
    /// <summary>
    /// Wrapper class for boolean responses to work with BaseController.ParseResponse
    /// </summary>
    public class BooleanResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;

        public BooleanResponse(bool success, string message = "")
        {
            Success = success;
            Message = message;
        }
    }

    /// <summary>
    /// Wrapper class for decimal responses to work with BaseController.ParseResponse
    /// </summary>
    public class DecimalResponse
    {
        public decimal Value { get; set; }
        public string Currency { get; set; } = string.Empty;

        public DecimalResponse(decimal value, string currency = "")
        {
            Value = value;
            Currency = currency;
        }
    }

    /// <summary>
    /// Wrapper class for string list responses
    /// </summary>
    public class StringListResponse
    {
        public List<string> Items { get; set; } = new List<string>();

        public StringListResponse(List<string> items)
        {
            Items = items;
        }
    }
}
