﻿using Configurations.Utility;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos.EnterpriseDomain;
using SuperAdmin.Service.Services.Contracts;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class EnterpricePlanController : BaseController
    {
        private readonly IEnterpricePlanService _enterpricePlanService;

        public EnterpricePlanController(IEnterpricePlanService enterpricePlanService)
        {
            _enterpricePlanService = enterpricePlanService;
        }

        /// <summary>
        /// create enterprice plan
        /// <param name="model"></param>
        /// </summary>       
        /// <returns></returns>
        [HttpPost("create-plan")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        public async Task<IActionResult> CreatePlan([FromBody] EnterpriseDto model)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"];
            //string authorizationHeader = HttpContext.Request.Headers["Authorization"];
            //string token = authorizationHeader.Substring("Bearer ".Length).Trim();
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;

            var response = await _enterpricePlanService.CreateEnterpricePlan(model, loggedInUser, subdomain, "");
            return ParseResponse(response);
        }

        [HttpPost("create-plan-existing-company")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        public async Task<IActionResult> CreatePlanForExistingCompany([FromBody] ExistingCompanyEnterprisePlanDto model)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"];
           
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;

            var response = await _enterpricePlanService.CreateEnterpricePlanForExistingCompany(model, loggedInUser, subdomain);
            return ParseResponse(response);
        }


        /// <summary>
        /// Get enterprice plan 
        /// </summary>       
        /// <returns></returns>
        [HttpGet("get-enterprice-plan")]        
        [ProducesResponseType(typeof(ApiResponse<List<GetEnterpriceDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<GetEnterpriceDto>>), 404)]
        public async Task<IActionResult> GetEnterpricePlan([FromQuery]PaginationParameters parameters, string? searchKeyword)
        {
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var subdomain = HttpContext.Request.Headers["subdomain"];
            var response = await _enterpricePlanService.GetEnterpricePlan(parameters,loggedInUser, subdomain,searchKeyword);
            return ParseResponse(response);
        }

       /// <summary>
       /// Edit enterprise plan
       /// </summary>
       /// <param name="model"></param>
       /// <returns></returns>
        [HttpPut("edit-plan")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        public async Task<IActionResult> EditPlan([FromBody] UpdateEnterprisePlanDto model)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"];
           
            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;

            var response = await _enterpricePlanService.EditEnterpricePlan(model, loggedInUser, subdomain);
            return ParseResponse(response);
        }

        /// <summary>
        /// Delete enterprise plan
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        [HttpDelete("delete-plan/{tenantId}/{enterPriseSubId}")]
        [ProducesResponseType(typeof(ApiResponse<object>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        public async Task<IActionResult> DeletePlan([FromRoute] [Required] string tenantId, [Required] string enterPriseSubId)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"];

            string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;

            var response = await _enterpricePlanService.DeleteEnterpricePlan(tenantId, loggedInUser, enterPriseSubId, subdomain);
            return ParseResponse(response);
        }
    }
}
