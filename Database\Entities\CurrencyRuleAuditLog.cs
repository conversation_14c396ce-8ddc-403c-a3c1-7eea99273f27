using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace SuperAdmin.Service.Database.Entities
{
    /// <summary>
    /// Represents an audit log entry for currency rule changes, tracking all modifications for compliance and history
    /// </summary>
    public class CurrencyRuleAuditLog : BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyRuleAuditLog"/> class
        /// </summary>
        public CurrencyRuleAuditLog()
        {
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Gets or sets the unique identifier for this audit log entry
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the currency rule that was modified
        /// </summary>
        public Guid CurrencyRuleId { get; set; }

        /// <summary>
        /// Gets or sets the application this audit log entry relates to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the type of action that was performed (Creation, Modification, Deletion)
        /// </summary>
        public ActivityActionType ActionType { get; set; }
        /// <summary>
        /// Gets or sets the previous value before the change (serialized as JSON)
        /// </summary>
        public string OldValue { get; set; }

        /// <summary>
        /// Gets or sets the new value after the change (serialized as JSON)
        /// </summary>
        public string NewValue { get; set; }

        /// <summary>
        /// Gets or sets the name of the field that was changed
        /// </summary>
        public string FieldChanged { get; set; }

        /// <summary>
        /// Gets or sets the reason provided for making this change
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Gets or sets the ID of the user who performed this action
        /// </summary>
        public string PerformedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the currency rule that was modified
        /// </summary>
        [ForeignKey("CurrencyRuleId")]
        public virtual CurrencyRule CurrencyRule { get; set; }

        /// <summary>
        /// Gets or sets the user who performed this action
        /// </summary>
        public virtual User PerformedByUser { get; set; }
    }
}
