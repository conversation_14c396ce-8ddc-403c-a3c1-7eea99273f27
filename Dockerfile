#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
#FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80 8080 8081
EXPOSE 443
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
#FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY . .
RUN dotnet restore "SuperAdmin.Service.csproj"
COPY . .
#WORKDIR "/src/SuperAdmin.Service"
WORKDIR .
RUN dotnet build "SuperAdmin.Service.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SuperAdmin.Service.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "SuperAdmin.Service.dll"]
