﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Models.Configurations;
using SuperAdmin.Service.Services.Contracts;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace SuperAdmin.Service.Services.Implementations
{
    public class TokenService : ITokenService
    {
        private readonly Jwt _jwtSettings;
        private readonly UserManager<User> _userManager;
        private readonly AppDbContext _dbContext;

        public TokenService(
            IOptions<Jwt> jwtSettings,
            UserManager<User> userManager, AppDbContext dbContext)
        {
            _jwtSettings = jwtSettings.Value;
            _userManager = userManager;
            _dbContext = dbContext;
        }

        //public async Task<(bool isSuccessful, string token)> GenerateToken(User user)
        //{
        //    try
        //    {
        //        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
        //        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        //        var claims = await GetClaims(user);

        //        var securityToken = new JwtSecurityToken(
        //            issuer: _jwtSettings.Issuer,
        //            audience: _jwtSettings.Audience,
        //            claims: claims,
        //            expires: DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryInMinutes),
        //            signingCredentials: credentials
        //            );

        //        string token = new JwtSecurityTokenHandler().WriteToken(securityToken);
        //        return (true, token);
        //    }
        //    catch (Exception)
        //    {
        //        return (false, string.Empty);
        //    }
        //}

        public async Task<(bool isSuccessful, string accessToken, string refreshToken)> GenerateToken(User user)
        {
            try
            {
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
                var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
                var claims = await GetClaims(user);

                var accessTokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryInMinutes);
                var accessToken = new JwtSecurityToken(
                    issuer: _jwtSettings.Issuer,
                    audience: _jwtSettings.Audience,
                    claims: claims,
                    expires: accessTokenExpiration,
                    signingCredentials: credentials
                );

                string jwtToken = new JwtSecurityTokenHandler().WriteToken(accessToken);

                // Generate a refresh token
                var refreshTokenExpiration = DateTime.UtcNow.AddMinutes(_jwtSettings.RefreshTokenExpiryInMinutes);
                var refreshToken = Guid.NewGuid().ToString();
                var isRefreshTokeExist = _dbContext.RefreshTokens.SingleOrDefault(x=>x.UserId == user.Id);
                if (isRefreshTokeExist == null)
                {
                    RefreshToken newrefreshToken = new()
                    {
                        JwtId = accessToken.Id,
                        Token = refreshToken,
                        UserId = user.Id,
                        IsUsed = false,
                        IsRevoked = false,
                        ExpiryDate = DateTime.UtcNow.AddMonths(1),
                    };

                    _dbContext.RefreshTokens.Add(newrefreshToken);
                }
                else
                {
                    isRefreshTokeExist.JwtId = accessToken.Id;
                    isRefreshTokeExist.Token = refreshToken;
                    isRefreshTokeExist.IsRevoked = false;
                    isRefreshTokeExist.IsUsed = false;
                    isRefreshTokeExist.ExpiryDate = DateTime.UtcNow.AddMonths(1);
                }
                
                await _dbContext.SaveChangesAsync();

                return (true, jwtToken, refreshToken);
            }
            catch (Exception ex)
            {
                return (false, string.Empty, string.Empty);
            }
        }


        /// <summary>
        /// This method gets all user's identity claims
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<IList<Claim>> GetClaims(User user)
        {
            var identityOptions = new IdentityOptions();
            List<Claim> claims = new()
            {
                new Claim(JwtRegisteredClaimNames.Email, user.Email!),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, DateTime.UtcNow.ToEpochTimestampInSeconds().ToString()),
                new Claim(identityOptions.ClaimsIdentity.UserIdClaimType, user.Id),
                new Claim(identityOptions.ClaimsIdentity.UserNameClaimType, user.UserName!),
            };

            var userRoles = await _userManager.GetRolesAsync(user);
            foreach (var userRole in userRoles)
            {
                claims.Add(new Claim(ClaimTypes.Role, userRole));
            }

            return claims;
        }
    }
}
