﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SuperAdmin.Service.Migrations
{
    /// <inheritdoc />
    public partial class added_currency_rule_related_tbls : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CurrencyConversionRates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    FromCurrency = table.Column<int>(type: "integer", nullable: false),
                    ToCurrency = table.Column<int>(type: "integer", nullable: false),
                    ExchangeRate = table.Column<decimal>(type: "numeric(18,6)", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Source = table.Column<string>(type: "text", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "text", nullable: false),
                    ModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrencyConversionRates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrencyConversionRates_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CurrencyConversionRates_AspNetUsers_ModifiedByUserId",
                        column: x => x.ModifiedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CurrencyRules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    BusinessLine = table.Column<string>(type: "text", nullable: false),
                    SellCurrency = table.Column<int>(type: "integer", nullable: false),
                    CostCurrency = table.Column<int>(type: "integer", nullable: false),
                    SellRate = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    CostAmount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    ExternalSupplier = table.Column<int>(type: "integer", nullable: false),
                    ExternalSupplierCost = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "text", nullable: false),
                    ModifiedByUserId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrencyRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrencyRules_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CurrencyRules_AspNetUsers_ModifiedByUserId",
                        column: x => x.ModifiedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CurrencyRuleAuditLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CurrencyRuleId = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    ActionType = table.Column<int>(type: "integer", nullable: false),
                    OldValue = table.Column<string>(type: "text", nullable: false),
                    NewValue = table.Column<string>(type: "text", nullable: false),
                    FieldChanged = table.Column<string>(type: "text", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    PerformedByUserId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrencyRuleAuditLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrencyRuleAuditLogs_AspNetUsers_PerformedByUserId",
                        column: x => x.PerformedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CurrencyRuleAuditLogs_CurrencyRules_CurrencyRuleId",
                        column: x => x.CurrencyRuleId,
                        principalTable: "CurrencyRules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CurrencyRuleVersions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CurrencyRuleId = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    VersionNumber = table.Column<int>(type: "integer", nullable: false),
                    SellRate = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    CostAmount = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    ExternalSupplier = table.Column<int>(type: "integer", nullable: false),
                    ExternalSupplierCost = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CurrencyRuleVersions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CurrencyRuleVersions_AspNetUsers_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CurrencyRuleVersions_CurrencyRules_CurrencyRuleId",
                        column: x => x.CurrencyRuleId,
                        principalTable: "CurrencyRules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyConversionRates_CreatedByUserId",
                table: "CurrencyConversionRates",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyConversionRates_ModifiedByUserId",
                table: "CurrencyConversionRates",
                column: "ModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRuleAuditLogs_CurrencyRuleId",
                table: "CurrencyRuleAuditLogs",
                column: "CurrencyRuleId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRuleAuditLogs_PerformedByUserId",
                table: "CurrencyRuleAuditLogs",
                column: "PerformedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRules_CreatedByUserId",
                table: "CurrencyRules",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRules_ModifiedByUserId",
                table: "CurrencyRules",
                column: "ModifiedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRuleVersions_CreatedByUserId",
                table: "CurrencyRuleVersions",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CurrencyRuleVersions_CurrencyRuleId",
                table: "CurrencyRuleVersions",
                column: "CurrencyRuleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CurrencyConversionRates");

            migrationBuilder.DropTable(
                name: "CurrencyRuleAuditLogs");

            migrationBuilder.DropTable(
                name: "CurrencyRuleVersions");

            migrationBuilder.DropTable(
                name: "CurrencyRules");
        }
    }
}
