﻿using Common.Services.Interfaces;
using Common.Services.Utility;
using Configurations.Utility;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SuperAdmin.Service.Commands.Implementation;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Hubs;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.SubscriptionDomain;
using SuperAdmin.Service.Models.Dtos.TicketDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Configuration;
using System.Data;
using System.Text;

namespace SuperAdmin.Service.Services.Implementations
{
    public class TicketService : ITicketService
    {
        private readonly AppDbContext _appDbContext;
        private readonly IUserService _userService;
        private readonly ITicketCategoryService _ticketCategoryService;
        private readonly IGCPBucketService _gcpBucketService;
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;
        private readonly TicketHub _ticketHub;
        private readonly IHubContext<TicketHub> _hubContext;
        private readonly HttpClient _httpClient;
        private string? _getChatHistory;

        public TicketService(AppDbContext appDbContext, ITicketCategoryService ticketCategoryService, IUserService userService, IGCPBucketService gcpBucketService, IWebHostEnvironment environment, IConfiguration configuration, IHubContext<TicketHub> hubContext, TicketHub ticketHub)
        {
            _appDbContext = appDbContext;
            _userService = userService;
            _ticketCategoryService = ticketCategoryService;
            _gcpBucketService = gcpBucketService;
            _environment = environment;
            _configuration = configuration;
            _hubContext = hubContext;
            _ticketHub = ticketHub;
            _getChatHistory = _configuration["LiveChatEndpoints:GetChatHistory"];
            var baseAddress = _configuration.GetSection("LiveChatEndpoints")?.GetSection("BaseUrl")?.Value;
            if (baseAddress == null)
                throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

            _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };
        }

        #region Create Ticket By Super admin
        /// <summary>
        /// This method creates a ticket
        /// </summary>
        /// <param name="superAdminCreateTicket"></param>
        public async Task<ApiResponse<TicketDto>> CreateTicket(SuperAdminCreateTicket superAdminCreateTicket)
        {
            if (CheckExistingSubjectForCustomer(superAdminCreateTicket.Subject, superAdminCreateTicket.CustomerEmail))
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Ticket with the subject '{superAdminCreateTicket.Subject}' already exists for customer!"
                };
            }

            if (!await _ticketCategoryService.IsExist(superAdminCreateTicket.CategoryId))
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket category '{superAdminCreateTicket.CategoryId}' not found!"
                };
            }
            if (superAdminCreateTicket.IsChatExist && string.IsNullOrWhiteSpace(superAdminCreateTicket.ChatId))
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Chat ID is required for an existing chat!"
                };

            User? user = null;
            if (superAdminCreateTicket.AssignedUserId != null)
            {
                user = await _userService.GetUser(superAdminCreateTicket.AssignedUserId);
                if (user == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"User id '{superAdminCreateTicket.AssignedUserId}' not found!"
                    };
                }
            }

            Ticket ticket = new()
            {
                CustomerName = superAdminCreateTicket.CustomerName,
                CustomerEmail = superAdminCreateTicket.CustomerEmail,
                TicketCategoryId = superAdminCreateTicket.CategoryId,
                Priority = superAdminCreateTicket.Priority,
                Subject = superAdminCreateTicket.Subject,
                AssignedUserId = user?.Id,
                Status = user?.Id != null ? TicketStatus.ASSIGNED : TicketStatus.PENDING,
                Message = superAdminCreateTicket.Message,
                Comment = superAdminCreateTicket.Comment,
                ReferenceId = UniqueIdentifierGenerator.GenerateUniqueTicketIdWithTimestamp(15),
                ChatId = superAdminCreateTicket.ChatId,
                IsChatExist = superAdminCreateTicket.IsChatExist,
            };

            if (superAdminCreateTicket.Files != null)
            {
                foreach (var file in superAdminCreateTicket.Files)
                {
                    Guid guid = Guid.NewGuid();
                    var fileTrimmed = file.FileName.Replace(" ", "");
                    var fileName = guid.ToString().Replace('-', '0').Replace('_', '0').ToUpper() + "-" + fileTrimmed;
                    var res = await _gcpBucketService.UploadFileAsync(file, fileName);
                    if (res is not null)
                    {
                        var url = await _gcpBucketService.GetSignedUrlAsync(fileName);
                        TicketAttachment attachment = new()
                        {
                            TicketId = ticket.Id,
                            Link = url,
                            FileName = fileName
                        };
                        ticket.Attachements.Add(attachment);
                    }
                    else
                        return new ApiResponse<TicketDto>
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"Failed to upload file!"
                        };
                }
            }

            await _appDbContext.AddAsync(ticket);
            await _appDbContext.SaveChangesAsync();
            var ticketDto = await BuildTicketDtoWithAttachments(ticket);

            await NotifyTenantClientAboutTicket(ticketDto);
            return new ApiResponse<TicketDto>
            {
                Data = ticketDto,
                ResponseCode = "200",
                ResponseMessage = $"Ticket with subject {ticket.Subject} created successfully"
            };
        }
        #endregion

        private async Task NotifyTenantClientAboutTicket(TicketDto ticket)
        {
            var connectionId = _ticketHub.GetConnectionIdByEmail(ticket.CustomerEmail);
            if (connectionId != null)
                await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveTicketUpdate", ticket);
        }

        #region Create Ticket
        /// <summary>
        /// This method creates a ticket
        /// </summary>
        /// <param name="adminCreateTicket"></param>
        public async Task<ApiResponse<TicketDto>> CreateTicket(AdminCreateTicket adminCreateTicket, string name, string email)
        {
            if (!await _ticketCategoryService.IsExist(adminCreateTicket.CategoryId))
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket category '{adminCreateTicket.CategoryId}' not found!"
                };
            }

            Ticket ticket = new()
            {
                CustomerName = name,
                CustomerEmail = email,
                TicketCategoryId = adminCreateTicket.CategoryId,
                Subject = adminCreateTicket.Subject,
                Message = adminCreateTicket.Message,
                ReferenceId = UniqueIdentifierGenerator.GenerateUniqueTicketIdWithTimestamp(15)
            };


            if (adminCreateTicket.Files != null)
            {
                foreach (var file in adminCreateTicket.Files)
                {
                    Guid guid = Guid.NewGuid();
                    var fileTrimmed = file.FileName.Replace(" ", "");
                    var fileName = guid.ToString().Replace('-', '0').Replace('_', '0').ToUpper() + "-" + fileTrimmed;
                    var res = await _gcpBucketService.UploadFileAsync(file, fileName);
                    if (res is not null)
                    {
                        var url = await _gcpBucketService.GetSignedUrlAsync(fileName);
                        TicketAttachment attachment = new()
                        {
                            TicketId = ticket.Id,
                            Link = url,
                            FileName = fileName
                        };
                        ticket.Attachements.Add(attachment);
                    }
                    else
                        return new ApiResponse<TicketDto>
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"Failed to upload file!"
                        };
                }
            }

            await _appDbContext.AddAsync(ticket);
            await _appDbContext.SaveChangesAsync();

            var ticketDto = await BuildTicketDtoWithAttachments(ticket);

            await _hubContext.Clients.Group("SuperAdmin").SendAsync("ReceiveTicketUpdate", ticket);
            return new ApiResponse<TicketDto>
            {
                Data = ticketDto,
                ResponseCode = "200",
                ResponseMessage = $"Ticket with subject {ticket.Subject} created successfully"
            };
        }
        #endregion

        #region Get Chat History By Chat Id
        public async Task<ApiResponse<ChatHistoryResponse>> GetChatHistoryByChatId(string chatId, string token)
        {

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", token);
            var requestUri = string.Format(_getChatHistory, chatId);
            var response = await _httpClient.GetAsync(requestUri);
            var jsonString = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<ChatHistoryResponse>(jsonString);
                if (result != null)
                    return new ApiResponse<ChatHistoryResponse>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Chat history fetched successfully"
                    };
                else
                    return new ApiResponse<ChatHistoryResponse>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "No record found."
                    };
            }
            else
            {
                var statusCode = (int)response.StatusCode;
                var errorMessage = jsonString;
                return new ApiResponse<ChatHistoryResponse>
                {
                    Data = null,
                    ResponseCode = statusCode.ToString(),
                    ResponseMessage = $"Failed to fetch data"
                };
            }
        }
        #endregion

        #region Create Enterprize Plan Or Contact Us Support Ticket
        public async Task<ApiResponse<bool>> CreateEnterprizePlanCreationTicket(EnterprizeCreationRequestDto model)
        {
            // Validations: If isForContactUs is true, then Subject and Message are required
            if (model.IsForContactUs && (string.IsNullOrWhiteSpace(model.Subject) || string.IsNullOrWhiteSpace(model.Message)))
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subject and Message are required for Contact Us enquiries!"
                };
            }

            // Check if the ticket already exists for the customer
            var existingTicket = await _appDbContext.EnterprizeCreationRequests
                .FirstOrDefaultAsync(tc => tc.Email.Equals(model.Email.Trim()) && tc.CompanyName.ToLower().Equals(model.CompanyName.Trim().ToLower()));
            if (existingTicket != null && !model.IsForContactUs)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = $"You already have a creation request for this company. Please reach out to support to get an update."
                };
            }

            var requestToAdd = model.ToEnterprizeCreationRequest();
            await _appDbContext.EnterprizeCreationRequests.AddAsync(requestToAdd);

            // Add the new ticket to the database
            var ticketPayload = new AdminCreateTicket
            {
                CategoryId = !model.IsForContactUs ? Guid.Parse(_configuration.GetValue<string>("SubscriptionCategoryTicketId")) : Guid.Parse(_configuration.GetValue<string>("ContactCategoryTicketId")), // Replace with the actual category ID
                Subject = model.IsForContactUs ? model.Subject : "Enterprise Plan Creation Support Ticket",
                Message = model.IsForContactUs ?
                        $@"<div style='font-family: Arial, sans-serif;'>
                    <h4 style='color: #333; margin-bottom: 15px;'>Contact Request Details</h4>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr><td style='padding: 5px 0; font-weight: bold; width: 30%;'>Name:</td><td style='padding: 5px 0;'>{model.FirstName} {model.LastName}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Email:</td><td style='padding: 5px 0;'>{model.Email}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Phone Number:</td><td style='padding: 5px 0;'>{model.PhoneNumber}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Subject:</td><td style='padding: 5px 0;'>{model.Subject}</td></tr>
                    </table>
                    <div style='margin-top: 15px;'>
                        <strong>Message:</strong><br/>
                        <div style='background-color: #f9f9f9; padding: 10px; border-left: 3px solid #07C0EA; margin-top: 5px; white-space: pre-wrap;'>{model.Message}</div>
                    </div>
                </div>" :
                        $@"<div style='font-family: Arial, sans-serif;'>
                    <h4 style='color: #333; margin-bottom: 15px;'>Enterprise Account Request Details</h4>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr><td style='padding: 5px 0; font-weight: bold; width: 30%;'>Company Name:</td><td style='padding: 5px 0;'>{model.CompanyName}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Name:</td><td style='padding: 5px 0;'>{model.FirstName} {model.LastName}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Email:</td><td style='padding: 5px 0;'>{model.Email}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Phone Number:</td><td style='padding: 5px 0;'>{model.PhoneNumber}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Company Size:</td><td style='padding: 5px 0;'>{model.CompanySize}</td></tr>
                        <tr><td style='padding: 5px 0; font-weight: bold;'>Application:</td><td style='padding: 5px 0;'>{model.Application}</td></tr>
                    </table>
                </div>"
            };

            var response = await CreateTicket(ticketPayload, model.FirstName + " " + model.LastName, model.Email);
            if (response.ResponseCode == "200")
            {
                // Notify the SuperAdmin
                var body = string.Empty;
                if (!model.IsForContactUs)
                    body = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/create_enterprize_acct_req_admin.html"));
                else
                    body = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/contact_us_enquiry_admin.html"));

                body = body
                    .Replace("{fullName}", model.FirstName + " " + model.LastName)
                    .Replace("{companyName}", model.CompanyName)
                    .Replace("{ticketId}", response?.Data?.ReferenceId)
                    .Replace("{email}", model.Email)
                    .Replace("{phoneNumber}", model.PhoneNumber)
                    .Replace("{companySize}", model.CompanySize)
                    .Replace("{application}", model.Application.ToString())
                    .Replace("{message}", model.Message);

                string subject = !model.IsForContactUs ? $"New Enterprize Plan Creation Request" : "Contact Us: Enquiry Received";
                await Utility.SendGridSendMail(body, "<EMAIL>", subject);
                await Utility.SendGridSendMail(body, "<EMAIL>", subject);

                // Notify the super admin group
                await _hubContext.Clients.Group("SuperAdmin").SendAsync("ReceiveTicketUpdate", response?.Data);

                // Send notification to the user - aknowledge the ticket creation
                var userBody = string.Empty;
                if (!model.IsForContactUs)
                    userBody = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/create_enterprize_acct_req_user.html"));
                else
                    userBody = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/contact_us_enquiry_user.html"));

                userBody = userBody
                    .Replace("{fullName}", model.FirstName + " " + model.LastName)
                    .Replace("{companyName}", model.CompanyName)
                    .Replace("{ticketId}", response?.Data?.ReferenceId)
                    .Replace("{email}", model.Email)
                    .Replace("{phoneNumber}", model.PhoneNumber)
                    .Replace("{companySize}", model.CompanySize)
                    .Replace("{application}", model.Application.ToString())
                    .Replace("{message}", model.Message);

                string userSubject = !model.IsForContactUs ? $"Enterprize Plan Creation Request" : "Enquiry Received";
                await Utility.SendGridSendMail(userBody, model.Email, userSubject);

                return new ApiResponse<bool>
                {
                    Data = true,
                    ResponseCode = "200",
                    ResponseMessage = "Ticket created successfully"
                };
            }
            else
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = response.ResponseCode,
                    ResponseMessage = response.ResponseMessage
                };
            }
        }
        #endregion

        #region Get Tickets
        /// <summary>
        /// This method filters tickets
        /// </summary>
        /// <param name="filter"></param>
        public async Task<ApiResponse<PaginationResult<TicketDto>>> GetTickets(SortTicket filter, bool isSuperAdmin, string? userId)
        {
            var query = _appDbContext.Tickets
                .Include(t => t.AssignedUser)
                .Where(t => !t.IsDeleted)
                .Include(t => t.TicketCategory)
                .Include(t => t.TicketReplies)
                .AsQueryable();

            if (!isSuperAdmin)
                query = query.Where(t => t.AssignedUserId != null && t.AssignedUserId.Equals(userId));

            query = ApplyIsReadFilter(query, filter.IsRead);
            query = ApplySearchKeyword(query, filter.SearchKeyword);
            query = ApplyStatusFilter(query, filter.TicketStatus);
            query = ApplyPriorityFilter(query, filter.Priority);
            query = ApplyCategoryFilter(query, filter.CategoryId);
            query = ApplySort(query, filter.SortBy, filter.OrderBy);


            var tickets = query.Select(t => BuildTicketDto(t));

            var data = await PaginationHelper.PaginateRecords(tickets, filter.Page!.Value, filter.PageSize!.Value);
            return new ApiResponse<PaginationResult<TicketDto>>
            {
                ResponseMessage = "Tickets fetched successfully!",
                Data = data,
                ResponseCode = "200",
            };
        }
        #endregion

        #region Get Tickets
        /// <summary>
        /// This method filters tickets
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="email"></param>
        public async Task<ApiResponse<PaginationResult<TicketDto>>> GetTickets(SortTicket filter, string? email)
        {
            var query = _appDbContext.Tickets
                .Where(t => t.CustomerEmail.Equals(email) && !t.IsDeleted)
                .Include(t => t.TicketCategory)
                .AsQueryable();

            query = ApplyIsReadFilter(query, filter.IsRead);
            query = ApplySearchKeyword(query, filter.SearchKeyword);
            query = ApplyStatusFilter(query, filter.TicketStatus);
            query = ApplyPriorityFilter(query, filter.Priority);
            query = ApplyCategoryFilter(query, filter.CategoryId);
            query = ApplySort(query, filter.SortBy, filter.OrderBy);

            var tickets = query.Select(t => BuildTicketDto(t));

            var data = await PaginationHelper.PaginateRecords(tickets, filter.Page!.Value, filter.PageSize!.Value);
            return new ApiResponse<PaginationResult<TicketDto>>
            {
                ResponseMessage = "Tickets fetched successfully!",
                Data = data,
                ResponseCode = "200",
            };
        }
        #endregion

        #region Get Assigned Tickets
        /// <summary>
        /// This method get assigned tickets
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="email"></param>
        public async Task<ApiResponse<PaginationResult<AssignedTicket>>> GetAssignedTickets(SortTicket filter)
        {
            var query = _appDbContext.Tickets
                .Where(t => !t.IsDeleted)
                .Include(t => t.TicketCategory)
                .Include(t => t.AssignedUser)
                .AsQueryable();

            query = ApplySearchKeywordAssignedTicket(query, filter.SearchKeyword);
            query = ApplyStatusFilter(query, filter.TicketStatus);
            query = ApplyPriorityFilter(query, filter.Priority);
            query = ApplyCategoryFilter(query, filter.CategoryId);
            query = ApplySort(query, filter.SortBy, filter.OrderBy);

            //var tickets = query.Select(t => BuildTicketDto(t));
            var tickets = query.Select(t => new AssignedTicket()
            {
                AssignedTo = $"{t.AssignedUser.FirstName} {t.AssignedUser.LastName}",
                Status = t.Status.ToString(),
                Priority = t.Priority.ToString(),
                DateAssigned = t.CreatedAt,
                DateCreated = t.CreatedAt,
                Category = t.TicketCategory.Name,
                Message = t.Message,
                Subject = t.Subject,
            });

            var data = await PaginationHelper.PaginateRecords(tickets, filter.Page!.Value, filter.PageSize!.Value);
            return new ApiResponse<PaginationResult<AssignedTicket>>
            {
                ResponseMessage = "Assigned tickets fetched successfully!",
                Data = data,
                ResponseCode = "200",
            };
        }
        #endregion

        #region Apply filters - Private Method
        /// <summary>
        /// This method check a status filter and apply to query
        /// </summary>
        /// <param name="query"></param>
        /// <param name="status"></param>
        private static IQueryable<Ticket> ApplyStatusFilter(IQueryable<Ticket> query, TicketStatus? status)
        {
            return status.HasValue
                ? query.Where(t => status.Equals(TicketStatus.UNRESOLVED) ? t.Status != status.Value : t.Status == status.Value)
                : query;
        }
        #endregion

        /// <summary>
        /// This method check a status filter and apply to query
        /// </summary>
        /// <param name="query"></param>
        /// <param name="isRead"></param>
        private static IQueryable<Ticket> ApplyIsReadFilter(IQueryable<Ticket> query, bool? isRead)
        {
            return isRead.HasValue
                ? query.Where(t => isRead == t.IsRead)
                : query;
        }

        /// <summary>
        /// This method check a status filter and apply to query
        /// </summary>
        /// <param name="query"></param>
        /// <param name="status"></param>
        private static IQueryable<Ticket> ApplyPriorityFilter(IQueryable<Ticket> query, Priority? priority)
        {
            return priority.HasValue
                ? query.Where(t => t.Priority == priority.Value)
                : query;
        }

        /// <summary>
        /// This method check a category filter and apply to query
        /// </summary>
        /// <param name="query"></param>
        /// <param name="category"></param>
        private static IQueryable<Ticket> ApplyCategoryFilter(IQueryable<Ticket> query, Guid? category)
        {
            return category.HasValue
                ? query.Where(t => t.TicketCategoryId == category.Value)
                : query;
        }

        /// <summary>
        /// This method applies sorting to the query.
        /// </summary>
        /// <param name="query">The query to be sorted.</param>
        /// <param name="sortBy">The order by enum value.</param>
        /// <param name="orderBy">The order by enum value.</param>
        private static IQueryable<Ticket> ApplySort(IQueryable<Ticket> query, TicketSort? sortBy, OrderBy? orderBy)
        {
            return (orderBy, sortBy) switch
            {
                (null, null) => query.OrderByDescending(t => t.CreatedAt),
                (OrderBy.RecentlyAdded, null) => query.OrderByDescending(t => t.CreatedAt),
                (OrderBy.Oldest, null) => query.OrderBy(t => t.CreatedAt),
                (null, TicketSort.CreatedAt) => query.OrderByDescending(t => t.CreatedAt),
                (OrderBy.RecentlyAdded, TicketSort.CreatedAt) => query.OrderByDescending(t => t.CreatedAt),
                (OrderBy.Oldest, TicketSort.CreatedAt) => query.OrderBy(t => t.CreatedAt),
                (null, TicketSort.Status) => query.OrderByDescending(t => t.Status),
                (OrderBy.RecentlyAdded, TicketSort.Status) => query.OrderByDescending(t => t.Status),
                (OrderBy.Oldest, TicketSort.Status) => query.OrderBy(t => t.Status),
                (null, TicketSort.Category) => query.OrderByDescending(t => t.TicketCategory.Name),
                (OrderBy.RecentlyAdded, TicketSort.Category) => query.OrderByDescending(t => t.TicketCategory.Name),
                (OrderBy.Oldest, TicketSort.Category) => query.OrderBy(t => t.TicketCategory.Name),
                _ => throw new ArgumentException("Invalid combination of orderBy and sortBy parameters."),
            };
        }

        /// <summary>
        /// This method applies search to the query.
        /// </summary>
        /// <param name="query">The query to be search.</param>
        /// <param name="searchKeyword">The search keyword.</param>
        private static IQueryable<Ticket> ApplySearchKeyword(IQueryable<Ticket> query, string? searchKeyword)
        {
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                searchKeyword = searchKeyword.ToLower();

                query = query.Where(t =>
                    t.CustomerName.ToLower().Contains(searchKeyword) ||
                    t.CustomerEmail.ToLower().Contains(searchKeyword) ||
                    t.TicketCategory.Name.ToLower().Contains(searchKeyword) ||
                    t.Subject.ToLower().Contains(searchKeyword) ||
                    t.Message.ToLower().Contains(searchKeyword));
            }
            return query;
        }

        private static IQueryable<Ticket> ApplySearchKeywordAssignedTicket(IQueryable<Ticket> query, string? searchKeyword)
        {
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                searchKeyword = searchKeyword.ToLower();

                query = query.Where(t =>
                    t.TicketCategory.Name.ToLower().Contains(searchKeyword) ||
                    t.Subject.ToLower().Contains(searchKeyword) ||
                    t.Message.ToLower().Contains(searchKeyword));
            }
            return query;
        }

        /// <summary>
        /// This method check an existing ticket category name
        /// </summary>
        /// <param name="subject"></param>
        /// <param name="customerEmail"></param>
        private bool CheckExistingSubjectForCustomer(string subject, string customerEmail)
        {
            return _appDbContext.Tickets
                .Where(tc => tc.CustomerEmail.Equals(customerEmail.Trim()) && tc.Subject.ToLower().Equals(subject.Trim().ToLower()))
                .Any();
        }

        #region Assign User
        /// <summary>
        /// This method to assign a user to a ticket
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="userToAssign"></param>
        public async Task<ApiResponse<TicketDto>> AssignUser(Guid ticketId, string userToAssign, string? comment, string performedBy)
        {
            using var transaction = _appDbContext.Database.BeginTransaction();
            try
            {
                var ticket = await GetTicketWithDetails(ticketId);
                if (ticket == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"Ticket id: '{ticketId}' not found!"
                    };
                }

                User? user = await _userService.GetUser(userToAssign);
                if (user == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"User id '{userToAssign}' not found!"
                    };
                }

                if (ticket.AssignedUserId != null && ticket.AssignedUserId.Equals(userToAssign))
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "409",
                        ResponseMessage = $"User is currently assigned to the ticket"
                    };

                ticket = await new AssignUserCommand(user).Execute(_appDbContext, ticket, performedBy);
                ticket.Comment = comment;
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();
                await transaction.CommitAsync();
                if (user.Email != null)
                {
                    User? assignedBy = await _userService.GetUser(performedBy);
                    if (assignedBy != null)
                        await SendAssignedMail(ticket, user, assignedBy);
                }

                return new ApiResponse<TicketDto>
                {
                    Data = BuildTicketDto(ticket),
                    ResponseCode = "200",
                    ResponseMessage = $"Ticket {ticket.Id} status updated successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to assign user to ticket. {ex.Message}"
                };
            }
        }
        #endregion

        /// <summary>
        /// This method to assign a user to a ticket
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="status"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<TicketDto>> ChangeStatus(Guid ticketId, TicketStatus status, string performedBy)
        {
            using var transaction = _appDbContext.Database.BeginTransaction();
            try
            {
                var ticket = await GetTicketWithDetails(ticketId);
                if (ticket == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"Ticket id: '{ticketId}' not found!"
                    };
                }

                if (ticket.Status.Equals(status))
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "409",
                        ResponseMessage = $"Status '{status}' is already assigned to the ticket"
                    };
                }

                ticket = await new ChangeStatusCommand(status).Execute(_appDbContext, ticket, performedBy);
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();
                await transaction.CommitAsync();
                if (status.Equals(TicketStatus.RESOLVED))
                {
                    var actionPerformedBy = await _userService.GetUser(performedBy);
                    if (actionPerformedBy != null)
                        await SendResolvedMail(ticket, actionPerformedBy);
                }

                return new ApiResponse<TicketDto>
                {
                    Data = BuildTicketDto(ticket),
                    ResponseCode = "200",
                    ResponseMessage = $"Ticket {ticket.Id} status updated successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to change ticket status. {ex.Message}"
                };
            }
        }

        /// <summary>
        /// This method to set a priority to a ticket
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="priority"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<TicketDto>> SetPriority(Guid ticketId, Priority priority, string performedBy)
        {
            using var transaction = _appDbContext.Database.BeginTransaction();
            try
            {
                var ticket = await GetTicketWithDetails(ticketId);
                if (ticket == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"Ticket id '{ticketId}' not found!"
                    };
                }


                if (ticket.Status == TicketStatus.RESOLVED)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "405",
                        ResponseMessage = $"Ticket id: '{ticketId}' is resolved!"
                    };
                }

                if (ticket.Priority.Equals(priority))
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "409",
                        ResponseMessage = $"Priority '{priority}' is already assigned to the ticket"
                    };
                }

                ticket = await new SetPriorityCommand(priority).Execute(_appDbContext, ticket, performedBy);
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();
                await transaction.CommitAsync();

                return new ApiResponse<TicketDto>
                {
                    Data = BuildTicketDto(ticket),
                    ResponseCode = "200",
                    ResponseMessage = $"Ticket {ticket.Id} priority updated successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to set priority to ticket. {ex.Message}"
                };
            }

        }

        /// <summary>
        /// This method perform all command on a ticket
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="command"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<TicketDto>> PerformAllAction(Guid ticketId, TicketCommand command, string performedBy)
        {
            using var transaction = _appDbContext.Database.BeginTransaction();
            try
            {
                var ticket = await GetTicketWithDetails(ticketId);
                if (ticket == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"Ticket id '{ticketId}' not found!"
                    };
                }

                if (ticket.Status == TicketStatus.RESOLVED)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "405",
                        ResponseMessage = $"Ticket id:'{ticketId}' is resolved!"
                    };
                }

                User? user = await _userService.GetUser(command.AssignUserId);
                if (user == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"User id '{command.AssignUserId}' not found!"
                    };
                }

                ticket = await new SetPriorityCommand(command.Priority).Execute(_appDbContext, ticket, performedBy);
                ticket = await new ChangeStatusCommand(command.TicketStatus).Execute(_appDbContext, ticket, performedBy);
                ticket = await new AssignUserCommand(user).Execute(_appDbContext, ticket, performedBy);
                ticket = await new DueDateCommand(command.DueDate).Execute(_appDbContext, ticket, performedBy);

                ticket.Comment = command.Comment;
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();
                await transaction.CommitAsync();

                return new ApiResponse<TicketDto>
                {
                    Data = BuildTicketDto(ticket),
                    ResponseCode = "200",
                    ResponseMessage = $"Ticket {ticket.Id} updated successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to perform action. {ex.Message}"
                };
            }
        }

        /// <summary>
        /// This method is to get a single ticket detail
        /// </summary>
        /// <param name="ticketId"></param>
        public async Task<ApiResponse<TicketDto>> GetTicket(Guid ticketId, bool isRead)
        {
            var ticket = await GetTicketWithDetails(ticketId);
            if (ticket == null)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket id '{ticketId}' not found!"
                };
            }
            if (isRead && !ticket.IsRead)
            {
                ticket.IsRead = true;
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();
            }
            return new ApiResponse<TicketDto>
            {
                Data = await BuildTicketDtoWithAttachments(ticket),
                ResponseCode = "200",
                ResponseMessage = $"Ticket '{ticket.Id}' fetched successfully!"
            };
        }

        /// <summary>
        /// This method is to transform ticket to ticketDto
        /// </summary>
        /// <param name="ticket"></param>
        private static TicketDto BuildTicketDto(Ticket ticket)
        {
            return new TicketDto
            {
                Id = ticket.Id,
                CustomerName = ticket.CustomerName,
                CustomerEmail = ticket.CustomerEmail,
                CategoryId = ticket.TicketCategoryId,
                AssignedUserId = ticket.AssignedUserId,
                Message = ticket.Message,
                Subject = ticket.Subject,
                Priority = ticket.Priority,
                CreatedAt = ticket.CreatedAt,
                Status = ticket.Status,
                DueDate = ticket.DueDate,
                ReferenceId = ticket.ReferenceId,
                IsRead = ticket.IsRead,
                TicketReplies = ticket.TicketReplies.Select(x => new TicketReplyDto() { IsRead = x.IsRead, Message = x.Message, Attachments = x.Attachements.Select(t => t.Link).ToList() }).ToList(),
                CategoryName = ticket.TicketCategory?.Name,
                TicketActionLogs = ticket.TicketActionLogs
                    .OrderByDescending(a => a.CreatedAt)
                    .Select(a => BuildTicketActivityLogDto(a))
                    .ToList(),
                IsChatExist = ticket.IsChatExist,
                ChatId = ticket.ChatId,
            };
        }


        private async Task<TicketDto> BuildTicketDtoWithAttachments(Ticket ticket)
        {
            var ticketDto = new TicketDto
            {
                Id = ticket.Id,
                CustomerName = ticket.CustomerName,
                CustomerEmail = ticket.CustomerEmail,
                CategoryId = ticket.TicketCategoryId,
                AssignedUserId = ticket.AssignedUserId,
                Message = ticket.Message,
                Subject = ticket.Subject,
                Priority = ticket.Priority,
                CreatedAt = ticket.CreatedAt,
                Status = ticket.Status,
                DueDate = ticket.DueDate,
                ReferenceId = ticket.ReferenceId,
                IsRead = ticket.IsRead,
                TicketReplies = await PrepareTicketReplies(ticket.TicketReplies),
                CategoryName = ticket.TicketCategory?.Name,
                Attachments = await PrepareAttachments(ticket.Attachements),
                TicketActionLogs = ticket.TicketActionLogs
                    .OrderByDescending(a => a.CreatedAt)
                    .Select(a => BuildTicketActivityLogDto(a))
                    .ToList(),
                IsChatExist = ticket.IsChatExist,
                ChatId = ticket.ChatId,
            };
            return ticketDto;
        }

        private async Task<List<TicketReplyDto>> PrepareTicketReplies(ICollection<TicketReply> replies)
        {
            var unreadList = replies.Where(r => !r.IsRead).ToList();
            if (unreadList.Count > 0)
            {
                foreach (var unread in unreadList)
                    unread.IsRead = true;

                _appDbContext.TicketReplies.UpdateRange(unreadList);
                await _appDbContext.SaveChangesAsync();
            }


            var tasks = replies.Select(async reply => new TicketReplyDto
            {
                Message = reply.Message,
                IsRead = reply.IsRead,
                Attachments = await PrepareAttachments(reply.Attachements)
            });

            return (await Task.WhenAll(tasks)).ToList();
        }

        private async Task<List<string>> PrepareAttachments(ICollection<ReplyAttachment> attachments)
        {
            if (attachments == null)
                return new List<string>();

            var expiredAttachments = attachments
                .Where(a => (DateTime.UtcNow - a.ModifiedAt).TotalMinutes > 5 && !string.IsNullOrEmpty(a.FileName))
                .ToList();

            var tasks = expiredAttachments.Select(async attachment =>
            {
                var url = await _gcpBucketService.GetSignedUrlAsync(attachment.FileName);
                attachment.Link = url;
                attachment.ModifiedAt = DateTime.UtcNow;
            });

            await Task.WhenAll(tasks);

            _appDbContext.UpdateRange(expiredAttachments);
            await _appDbContext.SaveChangesAsync();

            return attachments.Select(t => t.Link).ToList();
        }


        private async Task<List<string>> PrepareAttachments(ICollection<TicketAttachment> attachments)
        {
            if (attachments == null)
                return new List<string>();

            var expiredAttachments = attachments
                .Where(a => (DateTime.UtcNow - a.ModifiedAt).TotalMinutes > 5 && !string.IsNullOrEmpty(a.FileName))
                .ToList();

            foreach (var attachment in expiredAttachments)
            {
                var url = await _gcpBucketService.GetSignedUrlAsync(attachment.FileName);
                attachment.Link = url;
                attachment.ModifiedAt = DateTime.UtcNow;

            }
            _appDbContext.UpdateRange(expiredAttachments);
            await _appDbContext.SaveChangesAsync();
            return attachments.Select(t => t.Link).ToList();
        }

        /// <summary>
        /// This method is to transform ticket to ticketDto
        /// </summary>
        /// <param name="ticket"></param>
        private static TicketActionLogDto? BuildTicketActivityLogDto(TicketActionLog? log)
        {
            return log == null ? null : new TicketActionLogDto
            {
                ActionType = log.ActionType,
                OldValue = log.OldValue,
                NewValue = log.NewValue,
                CreatedAt = log.CreatedAt,
                PerformedByUserId = log.PerformedByUserId,
                Message = log.Message
            };
        }

        /// <summary>
        /// This method is to get a single ticket detail
        /// </summary>
        /// <param name="id"></param>
        private async Task<Ticket?> GetTicketWithDetails(Guid id)
        {
            return await _appDbContext.Tickets
                .Where(t => t.Id.Equals(id) && !t.IsDeleted)
                .Include(t => t.TicketCategory)
                .Include(t => t.TicketActionLogs)
                .Include(t => t.AssignedUser)
                .Include(t => t.Attachements)
                .Include(t => t.TicketReplies)
                .ThenInclude(r => r.Attachements)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// This method is to get ticket statistics by status
        /// </summary>
        /// <param name="assignedUserId"></param>
        public async Task<ApiResponse<TicketStatistics>> GetTicketStatistics(string assignedUserId)
        {
            var ticketStatistics = await _appDbContext.Tickets
                .Where(ticket => !ticket.IsDeleted && string.IsNullOrEmpty(assignedUserId) || ticket.AssignedUserId == assignedUserId)
                .GroupBy(ticket => 1)
                .Select(group => new TicketStatistics
                {
                    TotalAssigned = group.Count(),
                    ResolvedCount = group.Count(ticket => ticket.Status == TicketStatus.RESOLVED),
                    AssignedCount = group.Count(ticket => ticket.Status == TicketStatus.ASSIGNED)
                })
                .FirstOrDefaultAsync();

            return new ApiResponse<TicketStatistics>
            {
                Data = ticketStatistics ?? new TicketStatistics(),
                ResponseCode = "200",
                ResponseMessage = $"Ticket statistics fetched successfully!"
            };
        }


        /// <summary>
        /// This method is to get ticket by userid and status
        /// </summary>
        /// <param name="assignedUserId"></param>
        /// <param name="status"></param>
        public async Task<ApiResponse<List<TicketDto>>> GetTicketByUserIdAndStatus(string assignedUserId, TicketStatus status)
        {
            var tickets = await _appDbContext.Tickets
               .Where(ticket => !ticket.IsDeleted && string.IsNullOrEmpty(assignedUserId) || ticket.AssignedUserId == assignedUserId && ticket.Status == status)
               .ToListAsync();

            return new ApiResponse<List<TicketDto>>
            {
                Data = BuildListTicketDto(tickets),
                ResponseCode = "200",
                ResponseMessage = $"Ticket statistics fetched successfully!"
            };
        }

        /// <summary>
        /// This method is to set due date to a ticket
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="dateTime"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<TicketDto>> SetDueDate(Guid ticketId, DateTime dateTime, string performedBy)
        {
            try
            {
                var ticket = await GetTicketWithDetails(ticketId);
                if (ticket == null)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"Ticket id '{ticketId}' not found!"
                    };
                }

                if (ticket.Status == TicketStatus.RESOLVED)
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "405",
                        ResponseMessage = $"Ticket id:'{ticketId}' is resolved!"
                    };
                }

                if (ticket.DueDate.Equals(dateTime))
                {
                    return new ApiResponse<TicketDto>
                    {
                        ResponseCode = "409",
                        ResponseMessage = $"Due date '{dateTime}' is already assigned to the ticket"
                    };
                }


                ticket = await new DueDateCommand(dateTime).Execute(_appDbContext, ticket, performedBy);
                _appDbContext.Update(ticket);
                await _appDbContext.SaveChangesAsync();

                return new ApiResponse<TicketDto>
                {
                    Data = BuildTicketDto(ticket),
                    ResponseCode = "200",
                    ResponseMessage = $"Ticket '{ticket.Id}' due date set successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to set due date to ticket. {ex.Message}"
                };
            }
        }

        /// <summary>
        /// This method assign multiple tickets to a user
        /// </summary>
        /// <param name="bulkTicket"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<List<TicketDto>>> BulkAssign(BulkTicketAssign bulkTicket, string performedBy)
        {
            User? user = await _userService.GetUser(bulkTicket.UserToAssign);
            if (user == null)
            {
                return new ApiResponse<List<TicketDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"User id '{bulkTicket.UserToAssign}' not found!"
                };
            }

            var tickets = await _appDbContext.Tickets
                .Where(ticket => !ticket.IsDeleted && bulkTicket.Tickets.Contains(ticket.Id))
                .Include(t => t.TicketCategory)
                .Include(t => t.TicketActionLogs)
                .Include(t => t.AssignedUser)
                .ToListAsync();

            using var transaction = _appDbContext.Database.BeginTransaction();
            try
            {
                foreach (var ticket in tickets)
                {
                    var ticketToUpdate = ticket;
                    if (ticket.Status != TicketStatus.RESOLVED)
                    {
                        if (string.IsNullOrEmpty(ticket.AssignedUserId) || !ticket.AssignedUserId.Equals(bulkTicket.UserToAssign))
                            ticketToUpdate = await new AssignUserCommand(user).Execute(_appDbContext, ticketToUpdate, performedBy);

                        if (!ticket.Priority.Equals(bulkTicket.Priority))
                            ticketToUpdate = await new SetPriorityCommand(bulkTicket.Priority).Execute(_appDbContext, ticketToUpdate, performedBy);

                        if (!ticket.DueDate.Equals(bulkTicket.DueDate))
                            ticketToUpdate = await new DueDateCommand(bulkTicket.DueDate).Execute(_appDbContext, ticketToUpdate, performedBy);
                    }
                    ticketToUpdate.Comment = bulkTicket.Comment;
                    _appDbContext.Update(ticketToUpdate);
                }

                await _appDbContext.SaveChangesAsync();
                await transaction.CommitAsync();
                if (user.Email != null)
                {
                    User? assignedBy = await _userService.GetUser(performedBy);
                    if (assignedBy != null)
                    {
                        foreach (var ticket in tickets)
                        {
                            await SendAssignedMail(ticket, user, assignedBy);
                        }
                    }
                }

                return new ApiResponse<List<TicketDto>>
                {
                    Data = tickets.Select(t => BuildTicketDto(t)).ToList(),
                    ResponseCode = "200",
                    ResponseMessage = $"Tickets assigned successfully!"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<TicketDto>>
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Failed to perform action. {ex.Message}"
                };
            }
        }

        /// <summary>
        /// This method is to send a reply to a ticket.
        /// </summary>
        /// <param name="ticketId"></param>
        /// <param name="replyTicket"></param>
        /// <param name="performedBy"></param>
        public async Task<ApiResponse<List<ReplyDto>>> ReplyTicket(Guid ticketId, ReplyTicket replyTicket, string performedBy)
        {
            var ticket = await GetTicketWithDetails(ticketId);
            if (ticket == null)
            {
                return new ApiResponse<List<ReplyDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket id '{ticketId}' not found!"
                };
            }

            if (ticket.Status == TicketStatus.RESOLVED)
            {
                return new ApiResponse<List<ReplyDto>>
                {
                    ResponseCode = "405",
                    ResponseMessage = $"Ticket id:'{ticketId}' is resolved!"
                };
            }

            TicketReply ticketReply = new()
            {
                Message = replyTicket.Message ?? string.Empty,
                PostedByUserId = performedBy,
                TicketId = ticketId,
                UserType = replyTicket.ReplyFrom
            };

            if (replyTicket.Files != null)
            {
                foreach (var file in replyTicket.Files)
                {
                    Guid guid = Guid.NewGuid();
                    var fileTrimmed = file.FileName.Replace(" ", "");
                    var fileName = guid.ToString().Replace('-', '0').Replace('_', '0').ToUpper() + "-" + fileTrimmed;
                    var res = await _gcpBucketService.UploadFileAsync(file, fileName);
                    if (res is not null)
                    {
                        var url = await _gcpBucketService.GetSignedUrlAsync(fileName);
                        ReplyAttachment attachment = new()
                        {
                            TicketReplyId = ticketReply.Id,
                            Link = url,
                            FileName = fileName
                        };
                        ticketReply.Attachements.Add(attachment);
                    }
                    else
                        return new ApiResponse<List<ReplyDto>>
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"Failed to upload file!"
                        };
                }
            }

            await _appDbContext.TicketReplies.AddAsync(ticketReply);
            await _appDbContext.SaveChangesAsync();

            await SendReplyMail(ticket, ticketReply);

            var replies = await _appDbContext.TicketReplies
                .Where(tr => tr.TicketId.Equals(ticketId))
                .OrderByDescending(tr => tr.CreatedAt)
                .Select(tr => new ReplyDto()
                {
                    TicketId = tr.TicketId,
                    Message = tr.Message,
                    PostedByUserId = tr.PostedByUserId,
                    UserType = tr.UserType,
                    Attachments = tr.Attachements.Select(t => t.Link).ToList()
                })
                .ToListAsync();

            await NotifyClientAboutTicketReply(ticketReply, ticket);
            return new ApiResponse<List<ReplyDto>>
            {
                Data = replies,
                ResponseCode = "200",
                ResponseMessage = "Reply sent successfully!"
            };
        }

        public async Task NotifyClientAboutTicketReply(TicketReply ticketReply, Ticket ticket)
        {
            var ticketReplyDto = new ReplyDto()
            {
                TicketId = ticket.Id,
                Message = ticketReply.Message,
                PostedByUserId = ticketReply.PostedByUserId,
                UserType = ticketReply.UserType,
                Attachments = ticket.Attachements.Select(x => x.Link).ToList()
            };

            if (ticketReply.UserType != UserType.SUPER_ADMIN)
                await NotifySuperAdminClientAboutTicketReply(ticketReplyDto);
            else
                await NotifyTenantClientAboutTicketReply(ticketReplyDto, ticket);
        }

        public async Task NotifyTenantClientAboutTicketReply(ReplyDto replyDto, Ticket ticket)
        {
            var connectionId = _ticketHub.GetConnectionIdByEmail(ticket.CustomerEmail);
            if (connectionId != null)
                await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveTicketReplyUpdate", replyDto);
        }

        public async Task NotifySuperAdminClientAboutTicketReply(ReplyDto reply) => await _hubContext.Clients.Group("SuperAdmin").SendAsync("ReceiveTicketReplyUpdate", reply);

        private async Task SendReplyMail(Ticket ticket, TicketReply ticketReply)
        {
            var body = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/reply_ticket.html"));
            body = body
                .Replace("{receiverFullName}", ticket.CustomerName)
                .Replace("{ticketId}", ticket.ReferenceId)
                .Replace("{reply}", ticketReply.Message);

            StringBuilder attachmentLinksBuilder = new();
            foreach (var attachment in ticketReply.Attachements)
            {
                attachmentLinksBuilder.Append($"<a href='{attachment.Link}'></a><br/>");
            }

            body = body.Replace("{attachments}", attachmentLinksBuilder.ToString());
            string subject = $"Ticket : {ticket.ReferenceId} reply.";
            await Utility.SendGridSendMail(body, ticket.CustomerEmail, subject);
        }

        private async Task SendAssignedMail(Ticket ticket, User assignedTo, User assignedBy)
        {
            if (assignedTo != null && !string.IsNullOrEmpty(assignedTo.Email))
            {
                var body = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/assigned_ticket.html"));
                body = body
                    .Replace("{name}", ticket.AssignedUser.FirstName + " " + ticket.AssignedUser.LastName)
                    .Replace("{ticketId}", ticket.ReferenceId)
                    .Replace("{description}", ticket.Message)
                    .Replace("{priority}", ticket.Priority.ToString())
                    .Replace("{dueDate}", ticket.DueDate == DateTime.MinValue ? "Not set" : ticket.DueDate.ToShortDateString())
                    .Replace("{link}", _configuration["ApplicationHostUrl"] + "/request")
                    .Replace("{senderFullName}", assignedBy.FirstName + " " + assignedBy.LastName)
                    .Replace("{comment}", ticket.Comment);

                string subject = $"Assigned Ticket Notification";
                await Utility.SendGridSendMail(body, assignedTo.Email, subject);
            }
        }

        private async Task SendResolvedMail(Ticket ticket, User resolvedBy)
        {
            var body = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/resolved_ticket.html"));
            body = body
                .Replace("{receiverFullName}", ticket.CustomerName)
                .Replace("{ticketId}", ticket.ReferenceId)
                .Replace("{description}", ticket.Message)
                .Replace("{senderFullName}", resolvedBy.FirstName + " " + resolvedBy.LastName);

            string subject = $"Ticket Resolved";
            await Utility.SendGridSendMail(body, ticket.CustomerEmail, subject);
        }

        public async Task<ApiResponse<List<ReplyDto>>> GetReplies(Guid ticketId)
        {
            var ticket = await GetTicketWithDetails(ticketId);
            if (ticket == null)
            {
                return new ApiResponse<List<ReplyDto>>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket id '{ticketId}' not found!"
                };
            }

            var replies = await _appDbContext.TicketReplies
                 .Where(tr => tr.TicketId.Equals(ticketId))
                 .Select(tr => new ReplyDto() { })
                 .ToListAsync();

            return new ApiResponse<List<ReplyDto>>
            {
                Data = replies,
                ResponseCode = "200",
                ResponseMessage = "Replies fetch successfully!"
            };
        }

        public async Task<ApiResponse<TicketDto>> DeleteTicket(Guid ticketId)
        {
            var ticket = await GetTicketWithDetails(ticketId);
            if (ticket == null)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket id '{ticketId}' not found!"
                };
            }

            ticket.IsDeleted = true;
            _appDbContext.Tickets.Update(ticket);
            _appDbContext.SaveChanges();
            return new ApiResponse<TicketDto>
            {
                ResponseCode = "200",
                ResponseMessage = $"Ticket '{ticket.Id}' deleted successfully!"
            };
        }

        public async Task<ApiResponse<TicketDto>> DeleteTicket(Guid ticketId, string? email)
        {
            var ticket = await _appDbContext.Tickets
                .Where(t => t.Id.Equals(ticketId) && t.CustomerEmail.Equals(email))
                .FirstOrDefaultAsync();

            if (ticket == null)
            {
                return new ApiResponse<TicketDto>
                {
                    ResponseCode = "404",
                    ResponseMessage = $"Ticket id '{ticketId}' not found!"
                };
            }

            ticket.IsDeleted = true;
            _appDbContext.Tickets.Update(ticket);
            _appDbContext.SaveChanges();
            return new ApiResponse<TicketDto>
            {
                ResponseCode = "200",
                ResponseMessage = $"Ticket '{ticket.Id}' deleted successfully!"
            };
        }

        /// <summary>
        /// This method is to transform ticket list to ticketDto list
        /// </summary>
        /// <param name="ticket"></param>
        private static List<TicketDto> BuildListTicketDto(List<Ticket> tickets)
        {
            List<TicketDto> ticketList = new();

            foreach (var ticket in tickets)
            {
                TicketDto tickt = new()
                {
                    CustomerName = ticket.CustomerName,
                    CustomerEmail = ticket.CustomerEmail,
                    CategoryId = ticket.TicketCategoryId,
                    AssignedUserId = ticket.AssignedUserId,
                    Message = ticket.Message,
                    Subject = ticket.Subject,
                    Priority = ticket.Priority,
                    CreatedAt = ticket.CreatedAt,
                    Status = ticket.Status,
                    DueDate = ticket.DueDate,
                    ReferenceId = ticket.ReferenceId,
                    IsRead = ticket.IsRead,
                    TicketReplies = ticket.TicketReplies.Select(x => new TicketReplyDto() { IsRead = x.IsRead, Message = x.Message, Attachments = x.Attachements.Select(t => t.Link).ToList() }).ToList(),
                    CategoryName = ticket.TicketCategory?.Name,
                    TicketActionLogs = ticket.TicketActionLogs
                    .OrderByDescending(a => a.CreatedAt)
                    .Select(a => BuildTicketActivityLogDto(a))
                    .ToList()
                };

                ticketList.Add(tickt);
            }
            return ticketList;
        }

        public async Task<int> TicketCountByCategory(string category, TicketStatus status)
        {
            return await _appDbContext.Tickets
                .Where(ticket => ticket.TicketCategoryId.ToString() == category && ticket.Status.Equals(status))
                .CountAsync();
        }
    }
}

