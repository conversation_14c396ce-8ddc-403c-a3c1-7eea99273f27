using Configurations.Utility;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Models.Dtos.ContactDomain;
using SuperAdmin.Service.Services.Contracts;

namespace SuperAdmin.Service.Services.Implementations
{
    public class ContactService : IContactService
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;

        public ContactService(AppDbContext context, UserManager<User> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<ApiResponse<ContactDto>> CreateContact(string userId, CreateContactDto model)
        {
            if (model.Subdomain == "api")
            {
                return new ApiResponse<ContactDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid subdomain for contact upload"
                };
            }

            try
            {
                // Check if contact with same phone number already exists for this user
                var existingContact = await _context.Contacts
                    .FirstOrDefaultAsync(c => c.UserId == userId && c.PhoneNumber == model.PhoneNumber && !c.IsDeleted && c.Subdomain == model.Subdomain);

                if (existingContact != null)
                {
                    return new ApiResponse<ContactDto>
                    {
                        ResponseCode = "409",
                        ResponseMessage = "Contact with this phone number already exists"
                    };
                }

                // If email is provided, check if it already exists for this user
                if (!string.IsNullOrEmpty(model.Email))
                {
                    var existingEmailContact = await _context.Contacts
                        .FirstOrDefaultAsync(c => c.UserId == userId && c.Email == model.Email && !c.IsDeleted);

                    if (existingEmailContact != null)
                    {
                        return new ApiResponse<ContactDto>
                        {
                            ResponseCode = "409",
                            ResponseMessage = "Contact with this email already exists"
                        };
                    }
                }

                var contact = new Contact
                {
                    UserId = userId,
                    Name = model.Name,
                    Email = model.Email,
                    PhoneNumber = model.PhoneNumber,
                    Subdomain = model.Subdomain,
                    Industry = model.Industry
                };

                _context.Contacts.Add(contact);
                await _context.SaveChangesAsync();

                var contactDto = new ContactDto
                {
                    Id = contact.Id,
                    UserId = contact.UserId,
                    Name = contact.Name,
                    Email = contact.Email,
                    PhoneNumber = contact.PhoneNumber,
                    Industry = contact.Industry,
                    CreatedAt = contact.CreatedAt,
                    ModifiedAt = contact.ModifiedAt
                };

                return new ApiResponse<ContactDto>
                {
                    Data = contactDto,
                    ResponseCode = "201",
                    ResponseMessage = "Contact created successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<ContactDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<ContactUploadResponseDto>> UploadContacts(string userId, List<CreateContactDto> contacts)
        {
            var subdomain = contacts[0].Subdomain;
            if (subdomain == "api")
            {
                return new ApiResponse<ContactUploadResponseDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid subdomain for contact upload"
                };
            }

            try
            {
                var response = new ContactUploadResponseDto
                {
                    TotalContacts = contacts.Count
                };

                var existingPhoneNumbers = await _context.Contacts
                    .Where(c => c.UserId == userId && !c.IsDeleted && c.Subdomain == subdomain)
                    .Select(c => c.PhoneNumber)
                    .ToListAsync();

                var existingEmails = await _context.Contacts
                    .Where(c => c.UserId == userId && !c.IsDeleted && c.Email != null && c.Subdomain == subdomain)
                    .Select(c => c.Email)
                    .ToListAsync();

                if (contacts.Count < 500)
                    await ProcessContactUploads(userId, contacts, response, existingPhoneNumbers, existingEmails);
                else
                    BackgroundJob.Enqueue(() => ProcessContactUploads(userId, contacts, response, existingPhoneNumbers, existingEmails));

                return new ApiResponse<ContactUploadResponseDto>
                {
                    Data = response,
                    ResponseCode = "200",
                    ResponseMessage = $"Upload completed. {response.SuccessfulUploads} successful, {response.FailedUploads} failed"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<ContactUploadResponseDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred during upload: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<ContactDto>>> GetUserContacts(string userId, string subdomain)
        {
            try
            {
                var contacts = await _context.Contacts
                    .Where(c => c.UserId == userId && !c.IsDeleted && c.Subdomain == subdomain)
                    .Select(c => new ContactDto
                    {
                        Id = c.Id,
                        UserId = c.UserId,
                        Name = c.Name,
                        Email = c.Email,
                        PhoneNumber = c.PhoneNumber,
                        Industry = c.Industry,
                        Subdomain = c.Subdomain,
                        CreatedAt = c.CreatedAt,
                        ModifiedAt = c.ModifiedAt
                    })
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                return new ApiResponse<List<ContactDto>>
                {
                    Data = contacts,
                    ResponseCode = "200",
                    ResponseMessage = "Contacts retrieved successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<ContactDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<bool>> HasUserUploadedContacts(string userId, string subdomain)
        {
            if (subdomain == "api")
            {
                return new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid subdomain for contact upload"
                };
            }

            try
            {
                var hasContacts = await _context.Contacts
                    .AnyAsync(c => c.UserId == userId && !c.IsDeleted && c.Subdomain == subdomain);

                return new ApiResponse<bool>
                {
                    Data = hasContacts,
                    ResponseCode = "200",
                    ResponseMessage = hasContacts ? "User has uploaded contacts" : "User has not uploaded contacts"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<ContactDto>> UpdateContact(string userId, Guid contactId, CreateContactDto model)
        {
            try
            {
                var contact = await _context.Contacts
                    .FirstOrDefaultAsync(c => c.Id == contactId && c.UserId == userId && !c.IsDeleted);

                if (contact == null)
                {
                    return new ApiResponse<ContactDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Contact not found"
                    };
                }

                // Check if phone number is being changed and if new phone number already exists
                if (contact.PhoneNumber != model.PhoneNumber)
                {
                    var existingPhoneContact = await _context.Contacts
                        .FirstOrDefaultAsync(c => c.UserId == userId && c.PhoneNumber == model.PhoneNumber && !c.IsDeleted && c.Id != contactId);

                    if (existingPhoneContact != null)
                    {
                        return new ApiResponse<ContactDto>
                        {
                            ResponseCode = "409",
                            ResponseMessage = "Contact with this phone number already exists"
                        };
                    }
                }

                // Check if email is being changed and if new email already exists (only if email is provided)
                if (!string.IsNullOrEmpty(model.Email) && contact.Email != model.Email)
                {
                    var existingEmailContact = await _context.Contacts
                        .FirstOrDefaultAsync(c => c.UserId == userId && c.Email == model.Email && !c.IsDeleted && c.Id != contactId);

                    if (existingEmailContact != null)
                    {
                        return new ApiResponse<ContactDto>
                        {
                            ResponseCode = "409",
                            ResponseMessage = "Contact with this email already exists"
                        };
                    }
                }

                contact.Name = model.Name;
                contact.Email = model.Email;
                contact.PhoneNumber = model.PhoneNumber;
                contact.Industry = model.Industry;
                contact.ModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var contactDto = new ContactDto
                {
                    Id = contact.Id,
                    UserId = contact.UserId,
                    Name = contact.Name,
                    Email = contact.Email,
                    PhoneNumber = contact.PhoneNumber,
                    Subdomain = contact.Subdomain,
                    CreatedAt = contact.CreatedAt,
                    ModifiedAt = contact.ModifiedAt
                };

                return new ApiResponse<ContactDto>
                {
                    Data = contactDto,
                    ResponseCode = "200",
                    ResponseMessage = "Contact updated successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<ContactDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<dynamic>> DeleteContact(string userId, Guid contactId)
        {
            try
            {
                var contact = await _context.Contacts
                    .FirstOrDefaultAsync(c => c.Id == contactId && c.UserId == userId && !c.IsDeleted);

                if (contact == null)
                {
                    return new ApiResponse<dynamic>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Contact not found"
                    };
                }

                contact.IsDeleted = true;
                contact.ModifiedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new ApiResponse<dynamic>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Contact deleted successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<dynamic>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<List<ContactDto>>> GetAllContactsForSubdomain(string subdomain, int pageNumber, int pageSize)
        {
            if (subdomain == "api")
            {
                return new ApiResponse<List<ContactDto>>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid subdomain for contact retrieval"
                };
            }

            try
            {
                var contacts = await _context.Contacts
                    .Where(c => c.Subdomain == subdomain && !c.IsDeleted)
                    .OrderBy(c => c.Name)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(c => new ContactDto
                    {
                        Id = c.Id,
                        UserId = c.UserId,
                        Name = c.Name,
                        Email = c.Email,
                        PhoneNumber = c.PhoneNumber,
                        Industry = c.Industry,
                        Subdomain = c.Subdomain,
                        CreatedAt = c.CreatedAt,
                        ModifiedAt = c.ModifiedAt
                    })
                    .ToListAsync();

                return new ApiResponse<List<ContactDto>>
                {
                    Data = contacts,
                    ResponseCode = "200",
                    ResponseMessage = "Contacts retrieved successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<ContactDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        // Get contact by ID
        public async Task<ApiResponse<ContactDto>> GetContactById(Guid contactId)
        {
            try
            {
                var contact = await _context.Contacts
                    .FirstOrDefaultAsync(c => c.Id == contactId && !c.IsDeleted);

                if (contact == null)
                {
                    return new ApiResponse<ContactDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Contact not found"
                    };
                }

                var contactDto = new ContactDto
                {
                    Id = contact.Id,
                    UserId = contact.UserId,
                    Name = contact.Name,
                    Email = contact.Email,
                    PhoneNumber = contact.PhoneNumber,
                    Industry = contact.Industry,
                    Subdomain = contact.Subdomain,
                    CreatedAt = contact.CreatedAt,
                    ModifiedAt = contact.ModifiedAt
                };

                return new ApiResponse<ContactDto>
                {
                    Data = contactDto,
                    ResponseCode = "200",
                    ResponseMessage = "Contact retrieved successfully"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<ContactDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task ProcessContactUploads(string userId, List<CreateContactDto> contacts, ContactUploadResponseDto response, List<string> existingPhoneNumbers, List<string?> existingEmails)
        {
            var subdomain = contacts[0].Subdomain;
            foreach (var contactDto in contacts)
            {
                try
                {
                    // Skip if phone number already exists
                    if (existingPhoneNumbers.Contains(contactDto.PhoneNumber))
                    {
                        response.FailedUploads++;
                        response.ErrorMessages.Add($"Contact with phone number {contactDto.PhoneNumber} already exists");
                        continue;
                    }

                    // Skip if email already exists (only if email is provided)
                    if (!string.IsNullOrEmpty(contactDto.Email) && existingEmails.Contains(contactDto.Email))
                    {
                        response.FailedUploads++;
                        response.ErrorMessages.Add($"Contact with email {contactDto.Email} already exists");
                        continue;
                    }

                    var contact = new Contact
                    {
                        UserId = userId,
                        Name = contactDto.Name,
                        Email = contactDto.Email,
                        PhoneNumber = contactDto.PhoneNumber,
                        Subdomain = subdomain,
                        Industry = contactDto.Industry
                    };

                    _context.Contacts.Add(contact);
                    existingPhoneNumbers.Add(contactDto.PhoneNumber); // Add to list to prevent duplicates in same batch

                    if (!string.IsNullOrEmpty(contactDto.Email))
                    {
                        existingEmails.Add(contactDto.Email); // Add to list to prevent duplicates in same batch
                    }

                    response.SuccessfulUploads++;
                }
                catch (Exception ex)
                {
                    response.FailedUploads++;
                    response.ErrorMessages.Add($"Failed to upload contact {contactDto.Name} ({contactDto.PhoneNumber}): {ex.Message}");
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
