﻿namespace SuperAdmin.Service.Models.Dtos.TicketDomain
{
    public class ChatHistoryResponse
    {
        public Clientid clientId { get; set; }
        public string status { get; set; }
        public DateTime createdAt { get; set; }
        public List<Message> messages { get; set; }
        public DateTime updatedAt { get; set; }
        public string id { get; set; }
    }

    public class Clientid
    {
        public string jobId { get; set; }
        public string email { get; set; }
        public string tenantId { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }
        public string id { get; set; }
    }

    public class Message
    {
        public string messageType { get; set; }
        public string chatId { get; set; }
        public string content { get; set; }
        public List<object> files { get; set; }
        public bool isSystemGenerated { get; set; }
        public DateTime createdAt { get; set; }
        public Senderid senderId { get; set; }
        public DateTime updatedAt { get; set; }
        public string id { get; set; }
    }

    public class Senderid
    {
        public string jobId { get; set; }
        public string email { get; set; }
        public string tenantId { get; set; }
        public DateTime createdAt { get; set; }
        public DateTime updatedAt { get; set; }
    }

}
