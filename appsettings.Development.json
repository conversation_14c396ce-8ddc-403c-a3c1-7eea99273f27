{
  "AllowedHosts": "*",

  "ApplicationName": "jobpro",
  "ApplicationHostUrl": "https://sua.pactocoin.com",

  "ElasticConfiguration": {
    "Uri": "https://jobpro.es.us-central1.gcp.cloud.es.io",
    "CloudId": "Zarttech:dXMtY2VudHJhbDEuZ2NwLmNsb3VkLmVzLmlvOjQ0MyRlZmNlZWNjMmM5MjQ0ZGJiYWUzMGY3OWVlNTQ0MTZhZCQ2N2IxYzRhZjZjNTY0MjQwOGM1YjRiNjgzOGZhZDZiNA==",
    "Username": "elastic",
    "Password": "uGT1QlGNpJQ9GRuVcddQxJnN"
  },

  "RabbitMQ": {
    "HostName": "localhost",
    "Port": "5672"
  },

  "ConnectionStrings": {
    "ConnectionString": "Host=jobpro-dev.clciecgggu9z.eu-central-1.rds.amazonaws.com;Port=5432;Database=super*****;Username=postgres;Password=**********************$|SlNR;Include Error Detail=true",
    "LocalConnectionString": "Host=localhost;Port=5432;Database=super*****;Username=postgres;Password=*****;Include Error Detail=true"
  },

  "Twilio": {
    "AccountSID": "**********************************",
    "AuthToken": "********************************",
    "PhoneNumber": "+***********"
  },

  "Serilog": {
    "MinimumLevel": {
      "Default": "Warning",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    }
  },

  "CorsSettings": {
    "AllowedOrigins": [
      "http://localhost:4200",
      "http://localhost:3000",
      "https://sua.pactocoin.com",
      "https://*****.pactocoin.com",
      "*"
    ]
  },

  "Services": {
    "Auth": "http://localhost:5095",
    // "Monolith": "https://localhost:5001"
    "Monolith": "https://localhost:6001/"
  },

  "Jwt": {
    "Issuer": "app.jobpro.com",
    "Audience": "app.jobpro.com",
    "Key": "thdllsldd8kjfkdsslald90kp00okmmsddskaksmssml",
    "ExpiryInMinutes": 240,
    "Monolith": {
      "Issuer": "http://localhost",
      "Audience": "http://localhost",
      "Key": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765",
      "ExpiryInMinutes": 30
    }
  },

  "MAILGUN_API_KEY": "**************************************************",
  "MAILGUN_DOMAIN": "jobpro.app",

  "TwilioAccountSid": "**********************************",
  "TwilioApiSecret": "jbjjdQwwHZBjKPNOVhV8Qcv66UFg5DpC",
  "TwilioApiKey": "**********************************",
  "TwilioApiSID": "********************************",

  "WeavrConfigurations": {
    "API_KEY": "FkddGpWGZV0BjF5Xgk0ArQ==",
    //"BASE_URL": "https://localhost:5001/"
    //"BASE_URL": "https://wvr.jobpro.app/"
    "BASE_URL": "https://sandbox.weavr.io/multi/"
  },

  "GCSConfigOptions": {
    "BucketName": "jobpro-assets"
  },

  "WeavrEndpoints": {
    "CreateCorporateIdentity": "corporates",
    "SendEmailVerificationCodeForCorporates": "corporates/verification/email/send",
    "VerifyEmailForCorporates": "corporates/verification/email/verify",
    "CreatePassword": "passwords/{userid}/create",
    "LoginWithPassword": "login_with_password",
    "KYB": "corporates/kyb",
    "EnrolFor2FA": "authentication_factors/otp/{channel}",
    "VerifyCodeFor2FA": "authentication_factors/otp/{channel}/verify",
    "ManagedAccounts": "managed_accounts"
  },

  "WeavrProfiles": {
    "Corporates": "111567820722143405",
    "ManagedAccounts": "111567820778897581"
  },

  "AWSConfigOptions": {
    "AccessKey": "********************",
    "BucketName": "jobpro-public-dev-eu-central-1-************",
    "SecretKey": "7VoFakO5IYjiKSxkZ0L28RHlzqrxrB3UpRhZIs56",
    "Region": "eu-central-1",
    "ElasticCache": "master.redis-cluster.qop9dv.euc1.cache.amazonaws.com:6379",
    "ElasticCachePassword": "^h1r[R6PT8omXprpmK{U"
  },

  "RabbitMQConfiguration": {
    "Host": "localhost",
    "Port": "5672",
    "Username": "guest",
    "Password": "guest"
  },

  "SubscriptionCategoryTicketId": "287e3c78-d583-453e-8297-1c834c5e2095",
  "ContactCategoryTicketId": "4dc10a10-dd42-4609-92d1-181c66ce5b81"
}
