using AutoMapper;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Mappings
{
    /// <summary>
    /// AutoMapper profile for currency rule entity and DTO mappings
    /// </summary>
    public class CurrencyRuleMappingProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyRuleMappingProfile"/> class
        /// </summary>
        public CurrencyRuleMappingProfile()
        {
            CreateCurrencyRuleMappings();
            CreateCurrencyRuleVersionMappings();
            CreateCurrencyConversionRateMappings();
            CreateCurrencyRuleAuditLogMappings();
        }

        /// <summary>
        /// Creates mappings for CurrencyRule entity and related DTOs
        /// </summary>
        private void CreateCurrencyRuleMappings()
        {
            // CurrencyRule to CurrencyRuleDto
            CreateMap<CurrencyRule, CurrencyRuleDto>()
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => src.Application.ToString()))
                .ForMember(dest => dest.SellCurrency, opt => opt.MapFrom(src => src.SellCurrency.ToString()))
                .ForMember(dest => dest.CostCurrency, opt => opt.MapFrom(src => src.CostCurrency.ToString()))
                .ForMember(dest => dest.ExternalSupplier, opt => opt.MapFrom(src => src.ExternalSupplier.ToString()))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));

            // CreateCurrencyRuleDto to CurrencyRule
            CreateMap<CreateCurrencyRuleDto, CurrencyRule>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Versions, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => CurrencyRuleStatus.Active));

            // UpdateCurrencyRuleDto to CurrencyRule (for updates)
            CreateMap<UpdateCurrencyRuleDto, CurrencyRule>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Application, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByUserId, opt => opt.Ignore())
                .ForMember(dest => dest.Versions, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedAt, opt => opt.MapFrom(src => DateTime.UtcNow));
        }

        /// <summary>
        /// Creates mappings for CurrencyRuleVersion entity and related DTOs
        /// </summary>
        private void CreateCurrencyRuleVersionMappings()
        {
            // Basic mapping for CurrencyRuleVersion - most properties will be mapped manually due to complexity
            CreateMap<CurrencyRuleVersion, CurrencyRuleVersionDto>()
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => src.Application.ToString()))
                .ForMember(dest => dest.ExternalSupplier, opt => opt.MapFrom(src => src.ExternalSupplier.ToString()));
        }

        /// <summary>
        /// Creates mappings for CurrencyConversionRate entity and related DTOs
        /// </summary>
        private void CreateCurrencyConversionRateMappings()
        {
            // CurrencyConversionRate to CurrencyConversionRateDto
            CreateMap<CurrencyConversionRate, CurrencyConversionRateDto>()
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => src.Application.ToString()))
                .ForMember(dest => dest.FromCurrency, opt => opt.MapFrom(src => src.FromCurrency.ToString()))
                .ForMember(dest => dest.ToCurrency, opt => opt.MapFrom(src => src.ToCurrency.ToString()))
                .ForMember(dest => dest.Source, opt => opt.MapFrom(src => src.Source.ToString()));

            // CreateCurrencyConversionRateDto to CurrencyConversionRate
            CreateMap<CreateCurrencyConversionRateDto, CurrencyConversionRate>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedAt, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.Source, opt => opt.MapFrom(src => "Manual"));

            // UpdateCurrencyConversionRateDto to CurrencyConversionRate (for updates)
            CreateMap<UpdateCurrencyConversionRateDto, CurrencyConversionRate>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Application, opt => opt.Ignore())
                .ForMember(dest => dest.FromCurrency, opt => opt.Ignore())
                .ForMember(dest => dest.ToCurrency, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedByUserId, opt => opt.Ignore())
                .ForMember(dest => dest.ModifiedAt, opt => opt.MapFrom(src => DateTime.UtcNow));
        }

        /// <summary>
        /// Creates mappings for CurrencyRuleAuditLog entity and related DTOs
        /// </summary>
        private void CreateCurrencyRuleAuditLogMappings()
        {
            // CurrencyRuleAuditLog to CurrencyRuleAuditLogDto
            CreateMap<CurrencyRuleAuditLog, CurrencyRuleAuditLogDto>()
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => src.Application.ToString()))
                .ForMember(dest => dest.ActionType, opt => opt.MapFrom(src => src.ActionType.ToString()))
                .ForMember(dest => dest.Date, opt => opt.MapFrom(src => src.CreatedAt.ToString("yyyy-MM-dd")))
                .ForMember(dest => dest.Time, opt => opt.MapFrom(src => src.CreatedAt.ToString("HH:mm:ss")));
        }
    }
}
