using AutoMapper;
using Configurations.Utility;
using Microsoft.EntityFrameworkCore;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Text.Json;

namespace SuperAdmin.Service.Services.Implementations
{
    /// <summary>
    /// Service implementation for managing currency rules and conversion rates
    /// </summary>
    public class CurrencyRuleService : ICurrencyRuleService
    {
        private readonly AppDbContext _appDbContext;
        private readonly IActivityLogService _activityLogService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="CurrencyRuleService"/> class
        /// </summary>
        /// <param name="appDbContext">The database context</param>
        /// <param name="activityLogService">The activity log service</param>
        /// <param name="mapper">The AutoMapper instance</param>
        public CurrencyRuleService(AppDbContext appDbContext, IActivityLogService activityLogService, IMapper mapper)
        {
            _appDbContext = appDbContext;
            _activityLogService = activityLogService;
            _mapper = mapper;
        }

        /// <summary>
        /// Retrieves a paginated list of currency rules based on the specified filters
        /// </summary>
        /// <param name="filter">The filter criteria to apply</param>
        /// <returns>A paginated response containing currency rules</returns>
        public async Task<ApiResponse<PaginationResult<CurrencyRuleDto>>> GetCurrencyRules(CurrencyRuleFilterDto filter)
        {
            try
            {
                var query = _appDbContext.CurrencyRules
                    .Include(x => x.CreatedByUser)
                    .Include(x => x.ModifiedByUser)
                    .Include(x => x.Versions)
                    .AsQueryable();

                // Apply filters
                if (filter.Application.HasValue)
                {
                    query = query.Where(x => x.Application == filter.Application.Value);
                }

                if (!string.IsNullOrEmpty(filter.BusinessLine))
                {
                    query = query.Where(x => x.BusinessLine.Contains(filter.BusinessLine));
                }

                if (filter.SellCurrency.HasValue)
                {
                    query = query.Where(x => x.SellCurrency == filter.SellCurrency.Value);
                }

                if (filter.CostCurrency.HasValue)
                {
                    query = query.Where(x => x.CostCurrency == filter.CostCurrency.Value);
                }

                if (filter.Status.HasValue)
                {
                    query = query.Where(x => x.Status == filter.Status.Value);
                }

                if (filter.EffectiveDateFrom.HasValue)
                {
                    query = query.Where(x => x.EffectiveDate >= filter.EffectiveDateFrom.Value);
                }

                if (filter.EffectiveDateTo.HasValue)
                {
                    query = query.Where(x => x.EffectiveDate <= filter.EffectiveDateTo.Value);
                }

                if (!string.IsNullOrEmpty(filter.SearchKeyword))
                {
                    query = query.Where(x => x.BusinessLine.Contains(filter.SearchKeyword) ||
                                           x.SellCurrency.ToString().Contains(filter.SearchKeyword) ||
                                           x.CostCurrency.ToString().Contains(filter.SearchKeyword));
                }

                var totalCount = await query.CountAsync();
                var rules = await query
                    .OrderByDescending(x => x.CreatedAt)
                    .Skip((filter.Page - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var ruleDtos = _mapper.Map<List<CurrencyRuleDto>>(rules);

                // Set additional properties that require custom logic
                for (int i = 0; i < ruleDtos.Count; i++)
                {
                    var rule = rules[i];
                    ruleDtos[i].CreatedBy = $"{rule.CreatedByUser?.FirstName} {rule.CreatedByUser?.LastName}";
                    ruleDtos[i].ModifiedBy = $"{rule.ModifiedByUser?.FirstName} {rule.ModifiedByUser?.LastName}";
                    ruleDtos[i].VersionCount = rule.Versions.Count;
                }

                var paginationResult = new PaginationResult<CurrencyRuleDto>(
                    ruleDtos.ToArray(),
                    totalCount,
                    filter.Page,
                    filter.PageSize,
                    (int)Math.Ceiling((double)totalCount / filter.PageSize)
                );

                return new ApiResponse<PaginationResult<CurrencyRuleDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rules retrieved successfully",
                    Data = paginationResult
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<PaginationResult<CurrencyRuleDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Retrieves a specific currency rule by its unique identifier
        /// </summary>
        /// <param name="id">The unique identifier of the currency rule</param>
        /// <returns>The currency rule if found, otherwise an error response</returns>
        public async Task<ApiResponse<CurrencyRuleDto>> GetCurrencyRuleById(Guid id)
        {
            try
            {
                var rule = await _appDbContext.CurrencyRules
                    .Include(x => x.CreatedByUser)
                    .Include(x => x.ModifiedByUser)
                    .Include(x => x.Versions)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (rule == null)
                {
                    return new ApiResponse<CurrencyRuleDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency rule not found"
                    };
                }

                var ruleDto = _mapper.Map<CurrencyRuleDto>(rule);
                ruleDto.CreatedBy = $"{rule.CreatedByUser?.FirstName} {rule.CreatedByUser?.LastName}";
                ruleDto.ModifiedBy = $"{rule.ModifiedByUser?.FirstName} {rule.ModifiedByUser?.LastName}";
                ruleDto.VersionCount = rule.Versions.Count;

                return new ApiResponse<CurrencyRuleDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rule retrieved successfully",
                    Data = ruleDto
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyRuleDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Creates a new currency rule with the specified properties
        /// </summary>
        /// <param name="model">The currency rule data to create</param>
        /// <param name="userId">The ID of the user creating the rule</param>
        /// <returns>The created currency rule</returns>
        public async Task<ApiResponse<CurrencyRuleDto>> CreateCurrencyRule(CreateCurrencyRuleDto model, string userId)
        {
            try
            {
                // Check if a rule with the same application, business line and currencies already exists
                var existingRule = await _appDbContext.CurrencyRules
                    .FirstOrDefaultAsync(x => x.Application == model.Application &&
                                            x.BusinessLine == model.BusinessLine &&
                                            x.SellCurrency == model.SellCurrency &&
                                            x.CostCurrency == model.CostCurrency &&
                                            x.Status == CurrencyRuleStatus.Active);

                if (existingRule != null)
                {
                    return new ApiResponse<CurrencyRuleDto>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "A currency rule with the same application, business line and currencies already exists"
                    };
                }

                var currencyRule = _mapper.Map<CurrencyRule>(model);
                currencyRule.CreatedByUserId = userId;
                currencyRule.ModifiedByUserId = userId;

                _appDbContext.CurrencyRules.Add(currencyRule);

                // Create initial version
                var initialVersion = new CurrencyRuleVersion
                {
                    CurrencyRuleId = currencyRule.Id,
                    Application = model.Application,
                    VersionNumber = 1,
                    SellRate = model.SellRate,
                    CostAmount = model.CostAmount,
                    ExternalSupplier = model.ExternalSupplier,
                    ExternalSupplierCost = model.ExternalSupplierCost,
                    EffectiveDate = model.EffectiveDate,
                    Status = CurrencyRuleVersionStatus.Active,
                    CreatedByUserId = userId
                };

                _appDbContext.CurrencyRuleVersions.Add(initialVersion);

                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Creation,
                    "",
                    $"Created currency rule for {model.BusinessLine} - {model.SellCurrency} to {model.CostCurrency}",
                    "Currency Rule",
                    userId,
                    Applications.JobPays
                );

                // Log audit trail
                await LogCurrencyRuleAudit(currencyRule.Id, model.Application, ActivityActionType.Creation, "", JsonSerializer.Serialize(model), "Initial Creation", userId);

                return await GetCurrencyRuleById(currencyRule.Id);
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyRuleDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<CurrencyRuleDto>> UpdateCurrencyRule(Guid id, UpdateCurrencyRuleDto model, string userId)
        {
            try
            {
                var existingRule = await _appDbContext.CurrencyRules
                    .Include(x => x.Versions)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (existingRule == null)
                {
                    return new ApiResponse<CurrencyRuleDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency rule not found"
                    };
                }

                var oldValue = JsonSerializer.Serialize(new
                {
                    existingRule.SellRate,
                    existingRule.CostAmount,
                    existingRule.ExternalSupplier,
                    existingRule.ExternalSupplierCost,
                    existingRule.EffectiveDate
                });

                // Update the rule
                existingRule.SellRate = model.SellRate;
                existingRule.CostAmount = model.CostAmount;
                existingRule.ExternalSupplier = model.ExternalSupplier;
                existingRule.ExternalSupplierCost = model.ExternalSupplierCost;
                existingRule.EffectiveDate = model.EffectiveDate;
                existingRule.ModifiedByUserId = userId;
                existingRule.ModifiedAt = DateTime.UtcNow;

                // Create new version
                var nextVersionNumber = existingRule.Versions.Max(v => v.VersionNumber) + 1;
                var newVersion = new CurrencyRuleVersion
                {
                    CurrencyRuleId = existingRule.Id,
                    Application = existingRule.Application,
                    VersionNumber = nextVersionNumber,
                    SellRate = model.SellRate,
                    CostAmount = model.CostAmount,
                    ExternalSupplier = model.ExternalSupplier,
                    ExternalSupplierCost = model.ExternalSupplierCost,
                    EffectiveDate = model.EffectiveDate,
                    Status = CurrencyRuleVersionStatus.Active,
                    CreatedByUserId = userId
                };

                // Deactivate previous versions
                foreach (var version in existingRule.Versions)
                {
                    version.Status = CurrencyRuleVersionStatus.Inactive;
                }

                _appDbContext.CurrencyRuleVersions.Add(newVersion);
                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Modification,
                    oldValue,
                    $"Updated currency rule for {existingRule.BusinessLine} - {existingRule.SellCurrency} to {existingRule.CostCurrency}",
                    "Currency Rule",
                    userId,
                    Applications.JobPays
                );

                // Log audit trail
                await LogCurrencyRuleAudit(existingRule.Id, existingRule.Application, ActivityActionType.Modification, oldValue, JsonSerializer.Serialize(model), model.Reason ?? "Rule updated", userId);

                return await GetCurrencyRuleById(existingRule.Id);
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyRuleDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<BooleanResponse>> DeleteCurrencyRule(Guid id, string userId)
        {
            try
            {
                var rule = await _appDbContext.CurrencyRules.FirstOrDefaultAsync(x => x.Id == id);
                if (rule == null)
                {
                    return new ApiResponse<BooleanResponse>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency rule not found"
                    };
                }

                var oldValue = JsonSerializer.Serialize(rule);

                _appDbContext.CurrencyRules.Remove(rule);
                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Deletion,
                    oldValue,
                    $"Deleted currency rule for {rule.BusinessLine} - {rule.SellCurrency} to {rule.CostCurrency}",
                    "Currency Rule",
                    userId,
                    Applications.JobPays
                );

                // Log audit trail
                await LogCurrencyRuleAudit(rule.Id, rule.Application, ActivityActionType.Deletion, oldValue, "", "Rule deleted", userId);

                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rule deleted successfully",
                    Data = new BooleanResponse(true, "Currency rule deleted successfully")
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<BooleanResponse>> ActivateDeactivateCurrencyRule(Guid id, bool isActive, string userId)
        {
            try
            {
                var rule = await _appDbContext.CurrencyRules.FirstOrDefaultAsync(x => x.Id == id);
                if (rule == null)
                {
                    return new ApiResponse<BooleanResponse>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency rule not found"
                    };
                }

                var oldStatus = rule.Status.ToString();
                rule.Status = isActive ? CurrencyRuleStatus.Active : CurrencyRuleStatus.Inactive;
                rule.ModifiedByUserId = userId;
                rule.ModifiedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Modification,
                    oldStatus,
                    $"{(isActive ? "Activated" : "Deactivated")} currency rule for {rule.BusinessLine} - {rule.SellCurrency} to {rule.CostCurrency}",
                    "Currency Rule",
                    userId,
                    Applications.JobPays
                );

                // Log audit trail
                await LogCurrencyRuleAudit(rule.Id, rule.Application, ActivityActionType.Modification, oldStatus, rule.Status.ToString(), $"Status changed to {rule.Status}", userId);

                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Currency rule {(isActive ? "activated" : "deactivated")} successfully",
                    Data = new BooleanResponse(true, $"Currency rule {(isActive ? "activated" : "deactivated")} successfully")
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        private async Task LogCurrencyRuleAudit(Guid currencyRuleId, Applications application, ActivityActionType actionType, string oldValue, string newValue, string reason, string userId)
        {
            var auditLog = new CurrencyRuleAuditLog
            {
                CurrencyRuleId = currencyRuleId,
                Application = application,
                ActionType = actionType,
                OldValue = oldValue,
                NewValue = newValue,
                FieldChanged = "Multiple",
                Reason = reason,
                PerformedByUserId = userId
            };

            _appDbContext.CurrencyRuleAuditLogs.Add(auditLog);
            await _appDbContext.SaveChangesAsync();
        }

        public async Task<ApiResponse<PaginationResult<CurrencyRuleVersionDto>>> GetCurrencyRuleVersions(Guid currencyRuleId, int page = 1, int pageSize = 10)
        {
            try
            {
                var query = _appDbContext.CurrencyRuleVersions
                    .Include(x => x.CreatedByUser)
                    .Where(x => x.CurrencyRuleId == currencyRuleId)
                    .OrderByDescending(x => x.VersionNumber);

                var totalCount = await query.CountAsync();
                var versions = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var versionDtos = versions.Select(version => new CurrencyRuleVersionDto
                {
                    Id = version.Id,
                    Application = version.Application.ToString(),
                    VersionNumber = version.VersionNumber,
                    SellRate = version.SellRate,
                    CostAmount = version.CostAmount,
                    ExternalSupplier = version.ExternalSupplier.ToString(),
                    ExternalSupplierCost = version.ExternalSupplierCost,
                    EffectiveDate = version.EffectiveDate,
                    Status = version.Status.ToString(),
                    CreatedBy = $"{version.CreatedByUser?.FirstName} {version.CreatedByUser?.LastName}",
                    CreatedAt = version.CreatedAt
                }).ToList();

                var paginationResult = new PaginationResult<CurrencyRuleVersionDto>(
                    versionDtos.ToArray(),
                    totalCount,
                    page,
                    pageSize,
                    (int)Math.Ceiling((double)totalCount / pageSize)
                );

                return new ApiResponse<PaginationResult<CurrencyRuleVersionDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rule versions retrieved successfully",
                    Data = paginationResult
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<PaginationResult<CurrencyRuleVersionDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<CurrencyRuleVersionDto>> GetCurrencyRuleVersionById(Guid versionId)
        {
            try
            {
                var version = await _appDbContext.CurrencyRuleVersions
                    .Include(x => x.CreatedByUser)
                    .FirstOrDefaultAsync(x => x.Id == versionId);

                if (version == null)
                {
                    return new ApiResponse<CurrencyRuleVersionDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency rule version not found"
                    };
                }

                var versionDto = new CurrencyRuleVersionDto
                {
                    Id = version.Id,
                    Application = version.Application.ToString(),
                    VersionNumber = version.VersionNumber,
                    SellRate = version.SellRate,
                    CostAmount = version.CostAmount,
                    ExternalSupplier = version.ExternalSupplier.ToString(),
                    ExternalSupplierCost = version.ExternalSupplierCost,
                    EffectiveDate = version.EffectiveDate,
                    Status = version.Status.ToString(),
                    CreatedBy = $"{version.CreatedByUser?.FirstName} {version.CreatedByUser?.LastName}",
                    CreatedAt = version.CreatedAt
                };

                return new ApiResponse<CurrencyRuleVersionDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rule version retrieved successfully",
                    Data = versionDto
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyRuleVersionDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<PaginationResult<CurrencyConversionRateDto>>> GetCurrencyConversionRates(CurrencyConversionRateFilterDto filter)
        {
            try
            {
                var query = _appDbContext.CurrencyConversionRates
                    .Include(x => x.CreatedByUser)
                    .Include(x => x.ModifiedByUser)
                    .AsQueryable();

                // Apply filters
                if (filter.Application.HasValue)
                {
                    query = query.Where(x => x.Application == filter.Application.Value);
                }

                if (filter.FromCurrency.HasValue)
                {
                    query = query.Where(x => x.FromCurrency == filter.FromCurrency.Value);
                }

                if (filter.ToCurrency.HasValue)
                {
                    query = query.Where(x => x.ToCurrency == filter.ToCurrency.Value);
                }

                if (filter.IsActive.HasValue)
                {
                    query = query.Where(x => x.IsActive == filter.IsActive.Value);
                }

                if (filter.EffectiveDateFrom.HasValue)
                {
                    query = query.Where(x => x.EffectiveDate >= filter.EffectiveDateFrom.Value);
                }

                if (filter.EffectiveDateTo.HasValue)
                {
                    query = query.Where(x => x.EffectiveDate <= filter.EffectiveDateTo.Value);
                }

                if (!string.IsNullOrEmpty(filter.Source))
                {
                    query = query.Where(x => x.Source.Contains(filter.Source));
                }

                var totalCount = await query.CountAsync();
                var rates = await query
                    .OrderByDescending(x => x.CreatedAt)
                    .Skip((filter.Page - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var rateDtos = rates.Select(rate => new CurrencyConversionRateDto
                {
                    Id = rate.Id,
                    Application = rate.Application.ToString(),
                    FromCurrency = rate.FromCurrency.ToString(),
                    ToCurrency = rate.ToCurrency.ToString(),
                    ExchangeRate = rate.ExchangeRate,
                    EffectiveDate = rate.EffectiveDate,
                    ExpiryDate = rate.ExpiryDate,
                    IsActive = rate.IsActive,
                    Source = rate.Source,
                    CreatedBy = $"{rate.CreatedByUser?.FirstName} {rate.CreatedByUser?.LastName}",
                    ModifiedBy = $"{rate.ModifiedByUser?.FirstName} {rate.ModifiedByUser?.LastName}",
                    CreatedAt = rate.CreatedAt,
                    ModifiedAt = rate.ModifiedAt
                }).ToList();

                var paginationResult = new PaginationResult<CurrencyConversionRateDto>(
                    rateDtos.ToArray(),
                    totalCount,
                    filter.Page,
                    filter.PageSize,
                    (int)Math.Ceiling((double)totalCount / filter.PageSize)
                );

                return new ApiResponse<PaginationResult<CurrencyConversionRateDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency conversion rates retrieved successfully",
                    Data = paginationResult
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<PaginationResult<CurrencyConversionRateDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<CurrencyConversionRateDto>> GetCurrencyConversionRateById(Guid id)
        {
            try
            {
                var rate = await _appDbContext.CurrencyConversionRates
                    .Include(x => x.CreatedByUser)
                    .Include(x => x.ModifiedByUser)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (rate == null)
                {
                    return new ApiResponse<CurrencyConversionRateDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency conversion rate not found"
                    };
                }

                var rateDto = new CurrencyConversionRateDto
                {
                    Id = rate.Id,
                    Application = rate.Application.ToString(),
                    FromCurrency = rate.FromCurrency.ToString(),
                    ToCurrency = rate.ToCurrency.ToString(),
                    ExchangeRate = rate.ExchangeRate,
                    EffectiveDate = rate.EffectiveDate,
                    ExpiryDate = rate.ExpiryDate,
                    IsActive = rate.IsActive,
                    Source = rate.Source,
                    CreatedBy = $"{rate.CreatedByUser?.FirstName} {rate.CreatedByUser?.LastName}",
                    ModifiedBy = $"{rate.ModifiedByUser?.FirstName} {rate.ModifiedByUser?.LastName}",
                    CreatedAt = rate.CreatedAt,
                    ModifiedAt = rate.ModifiedAt
                };

                return new ApiResponse<CurrencyConversionRateDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency conversion rate retrieved successfully",
                    Data = rateDto
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyConversionRateDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<CurrencyConversionRateDto>> CreateCurrencyConversionRate(CreateCurrencyConversionRateDto model, string userId)
        {
            try
            {
                // Check if a rate with the same application and currencies already exists and is active
                var existingRate = await _appDbContext.CurrencyConversionRates
                    .FirstOrDefaultAsync(x => x.Application == model.Application &&
                                            x.FromCurrency == model.FromCurrency &&
                                            x.ToCurrency == model.ToCurrency &&
                                            x.IsActive);

                if (existingRate != null)
                {
                    // Deactivate existing rate
                    existingRate.IsActive = false;
                    existingRate.ModifiedByUserId = userId;
                    existingRate.ModifiedAt = DateTime.UtcNow;
                }

                var conversionRate = new CurrencyConversionRate
                {
                    Application = model.Application,
                    FromCurrency = model.FromCurrency,
                    ToCurrency = model.ToCurrency,
                    ExchangeRate = model.ExchangeRate,
                    EffectiveDate = model.EffectiveDate,
                    ExpiryDate = model.ExpiryDate,
                    IsActive = true,
                    Source = model.Source,
                    CreatedByUserId = userId,
                    ModifiedByUserId = userId
                };

                _appDbContext.CurrencyConversionRates.Add(conversionRate);
                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Creation,
                    "",
                    $"Created currency conversion rate from {model.FromCurrency} to {model.ToCurrency}",
                    "Currency Conversion Rate",
                    userId,
                    Applications.JobPays
                );

                return await GetCurrencyConversionRateById(conversionRate.Id);
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyConversionRateDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<CurrencyConversionRateDto>> UpdateCurrencyConversionRate(Guid id, UpdateCurrencyConversionRateDto model, string userId)
        {
            try
            {
                var existingRate = await _appDbContext.CurrencyConversionRates
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (existingRate == null)
                {
                    return new ApiResponse<CurrencyConversionRateDto>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency conversion rate not found"
                    };
                }

                var oldValue = JsonSerializer.Serialize(new
                {
                    existingRate.ExchangeRate,
                    existingRate.EffectiveDate,
                    existingRate.ExpiryDate,
                    existingRate.Source
                });

                // Update the rate
                existingRate.ExchangeRate = model.ExchangeRate;
                existingRate.EffectiveDate = model.EffectiveDate;
                existingRate.ExpiryDate = model.ExpiryDate;
                existingRate.Source = model.Source;
                existingRate.ModifiedByUserId = userId;
                existingRate.ModifiedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Modification,
                    oldValue,
                    $"Updated currency conversion rate from {existingRate.FromCurrency} to {existingRate.ToCurrency}",
                    "Currency Conversion Rate",
                    userId,
                    Applications.JobPays
                );

                return await GetCurrencyConversionRateById(existingRate.Id);
            }
            catch (Exception ex)
            {
                return new ApiResponse<CurrencyConversionRateDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<BooleanResponse>> DeleteCurrencyConversionRate(Guid id, string userId)
        {
            try
            {
                var rate = await _appDbContext.CurrencyConversionRates.FirstOrDefaultAsync(x => x.Id == id);
                if (rate == null)
                {
                    return new ApiResponse<BooleanResponse>
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Currency conversion rate not found"
                    };
                }

                var oldValue = JsonSerializer.Serialize(rate);

                _appDbContext.CurrencyConversionRates.Remove(rate);
                await _appDbContext.SaveChangesAsync();

                // Log activity
                await _activityLogService.LogActivity(
                    ActivityActionType.Deletion,
                    oldValue,
                    $"Deleted currency conversion rate from {rate.FromCurrency} to {rate.ToCurrency}",
                    "Currency Conversion Rate",
                    userId,
                    Applications.JobPays
                );

                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency conversion rate deleted successfully",
                    Data = new BooleanResponse(true, "Currency conversion rate deleted successfully")
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<BooleanResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<BulkImportResultDto>> BulkImportCurrencyRules(BulkCurrencyRuleImportDto model, string userId)
        {
            try
            {
                var result = new BulkImportResultDto
                {
                    TotalRecords = model.Rules.Count,
                    SuccessfulImports = 0,
                    FailedImports = 0,
                    Errors = new List<ImportErrorDto>()
                };

                for (int i = 0; i < model.Rules.Count; i++)
                {
                    var rule = model.Rules[i];
                    try
                    {
                        // Validate and parse enum values
                        if (!Enum.TryParse<CurrencyCode>(rule.SellCurrency, out var sellCurrency))
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = $"Invalid sell currency: {rule.SellCurrency}",
                                Data = new CurrencyRateImportItem
                                {
                                    FromCurrency = rule.SellCurrency,
                                    ToCurrency = rule.CostCurrency,
                                    ExchangeRate = rule.SellRate,
                                    EffectiveDate = rule.EffectiveDate,
                                    Source = "Bulk Import"
                                }
                            });
                            result.FailedImports++;
                            continue;
                        }

                        if (!Enum.TryParse<CurrencyCode>(rule.CostCurrency, out var costCurrency))
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = $"Invalid cost currency: {rule.CostCurrency}",
                                Data = new CurrencyRateImportItem
                                {
                                    FromCurrency = rule.SellCurrency,
                                    ToCurrency = rule.CostCurrency,
                                    ExchangeRate = rule.SellRate,
                                    EffectiveDate = rule.EffectiveDate,
                                    Source = "Bulk Import"
                                }
                            });
                            result.FailedImports++;
                            continue;
                        }

                        if (!Enum.TryParse<ExternalSupplierType>(rule.ExternalSupplier, out var externalSupplier))
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = $"Invalid external supplier type: {rule.ExternalSupplier}",
                                Data = new CurrencyRateImportItem
                                {
                                    FromCurrency = rule.SellCurrency,
                                    ToCurrency = rule.CostCurrency,
                                    ExchangeRate = rule.SellRate,
                                    EffectiveDate = rule.EffectiveDate,
                                    Source = "Bulk Import"
                                }
                            });
                            result.FailedImports++;
                            continue;
                        }

                        // Check if rule already exists (assuming default application for bulk import)
                        var existingRule = await _appDbContext.CurrencyRules
                            .FirstOrDefaultAsync(x => x.Application == Applications.JobPays &&
                                                    x.BusinessLine == rule.BusinessLine &&
                                                    x.SellCurrency == sellCurrency &&
                                                    x.CostCurrency == costCurrency &&
                                                    x.Status == CurrencyRuleStatus.Active);

                        if (existingRule != null)
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = "Currency rule with same business line and currencies already exists",
                                Data = new CurrencyRateImportItem
                                {
                                    FromCurrency = rule.SellCurrency,
                                    ToCurrency = rule.CostCurrency,
                                    ExchangeRate = rule.SellRate,
                                    EffectiveDate = rule.EffectiveDate,
                                    Source = "Bulk Import"
                                }
                            });
                            result.FailedImports++;
                            continue;
                        }

                        // Create new rule
                        var createDto = new CreateCurrencyRuleDto
                        {
                            Application = Applications.JobPays, // Default application for bulk import
                            BusinessLine = rule.BusinessLine,
                            SellCurrency = sellCurrency,
                            CostCurrency = costCurrency,
                            SellRate = rule.SellRate,
                            CostAmount = rule.CostAmount,
                            ExternalSupplier = externalSupplier,
                            ExternalSupplierCost = rule.ExternalSupplierCost,
                            EffectiveDate = rule.EffectiveDate
                        };

                        var createResult = await CreateCurrencyRule(createDto, userId);
                        if (createResult.ResponseCode == "200")
                        {
                            result.SuccessfulImports++;
                        }
                        else
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = createResult.ResponseMessage,
                                Data = new CurrencyRateImportItem
                                {
                                    FromCurrency = rule.SellCurrency,
                                    ToCurrency = rule.CostCurrency,
                                    ExchangeRate = rule.SellRate,
                                    EffectiveDate = rule.EffectiveDate,
                                    Source = "Bulk Import"
                                }
                            });
                            result.FailedImports++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add(new ImportErrorDto
                        {
                            RowNumber = i + 1,
                            Error = ex.Message,
                            Data = new CurrencyRateImportItem
                            {
                                FromCurrency = rule.SellCurrency,
                                ToCurrency = rule.CostCurrency,
                                ExchangeRate = rule.SellRate,
                                EffectiveDate = rule.EffectiveDate,
                                Source = "Bulk Import"
                            }
                        });
                        result.FailedImports++;
                    }
                }

                return new ApiResponse<BulkImportResultDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Bulk import completed. {result.SuccessfulImports} successful, {result.FailedImports} failed.",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<BulkImportResultDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred during bulk import: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<BulkImportResultDto>> BulkImportCurrencyConversionRates(BulkCurrencyRateImportDto model, string userId)
        {
            try
            {
                var result = new BulkImportResultDto
                {
                    TotalRecords = model.Rates.Count,
                    SuccessfulImports = 0,
                    FailedImports = 0,
                    Errors = new List<ImportErrorDto>()
                };

                for (int i = 0; i < model.Rates.Count; i++)
                {
                    var rate = model.Rates[i];
                    try
                    {
                        // Validate and parse enum values
                        if (!Enum.TryParse<CurrencyCode>(rate.FromCurrency, out var fromCurrency))
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = $"Invalid from currency: {rate.FromCurrency}",
                                Data = rate
                            });
                            result.FailedImports++;
                            continue;
                        }

                        if (!Enum.TryParse<CurrencyCode>(rate.ToCurrency, out var toCurrency))
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = $"Invalid to currency: {rate.ToCurrency}",
                                Data = rate
                            });
                            result.FailedImports++;
                            continue;
                        }

                        // Create new rate
                        var createDto = new CreateCurrencyConversionRateDto
                        {
                            Application = Applications.JobPays, // Default application for bulk import
                            FromCurrency = fromCurrency,
                            ToCurrency = toCurrency,
                            ExchangeRate = rate.ExchangeRate,
                            EffectiveDate = rate.EffectiveDate,
                            ExpiryDate = rate.ExpiryDate,
                            Source = rate.Source
                        };

                        var createResult = await CreateCurrencyConversionRate(createDto, userId);
                        if (createResult.ResponseCode == "200")
                        {
                            result.SuccessfulImports++;
                        }
                        else
                        {
                            result.Errors.Add(new ImportErrorDto
                            {
                                RowNumber = i + 1,
                                Error = createResult.ResponseMessage,
                                Data = rate
                            });
                            result.FailedImports++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add(new ImportErrorDto
                        {
                            RowNumber = i + 1,
                            Error = ex.Message,
                            Data = rate
                        });
                        result.FailedImports++;
                    }
                }

                return new ApiResponse<BulkImportResultDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Bulk import completed. {result.SuccessfulImports} successful, {result.FailedImports} failed.",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<BulkImportResultDto>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred during bulk import: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<PaginationResult<CurrencyRuleAuditLogDto>>> GetCurrencyRuleAuditLogs(CurrencyRuleAuditFilterDto filter)
        {
            try
            {
                var query = _appDbContext.CurrencyRuleAuditLogs
                    .Include(x => x.PerformedByUser)
                    .AsQueryable();

                // Apply filters
                if (filter.Application.HasValue)
                {
                    query = query.Where(x => x.Application == filter.Application.Value);
                }

                if (filter.CurrencyRuleId.HasValue)
                {
                    query = query.Where(x => x.CurrencyRuleId == filter.CurrencyRuleId.Value);
                }

                if (filter.ActionType.HasValue)
                {
                    query = query.Where(x => x.ActionType == filter.ActionType.Value);
                }

                if (filter.DateFrom.HasValue)
                {
                    query = query.Where(x => x.CreatedAt >= filter.DateFrom.Value);
                }

                if (filter.DateTo.HasValue)
                {
                    query = query.Where(x => x.CreatedAt <= filter.DateTo.Value);
                }

                if (!string.IsNullOrEmpty(filter.PerformedBy))
                {
                    query = query.Where(x => x.PerformedByUser.FirstName.Contains(filter.PerformedBy) ||
                                           x.PerformedByUser.LastName.Contains(filter.PerformedBy));
                }

                if (!string.IsNullOrEmpty(filter.FieldChanged))
                {
                    query = query.Where(x => x.FieldChanged.Contains(filter.FieldChanged));
                }

                var totalCount = await query.CountAsync();
                var auditLogs = await query
                    .OrderByDescending(x => x.CreatedAt)
                    .Skip((filter.Page - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var auditLogDtos = auditLogs.Select(log => new CurrencyRuleAuditLogDto
                {
                    Id = log.Id,
                    CurrencyRuleId = log.CurrencyRuleId,
                    Application = log.Application.ToString(),
                    ActionType = log.ActionType.ToString(),
                    OldValue = log.OldValue,
                    NewValue = log.NewValue,
                    FieldChanged = log.FieldChanged,
                    Reason = log.Reason,
                    PerformedBy = $"{log.PerformedByUser?.FirstName} {log.PerformedByUser?.LastName}",
                    Timestamp = log.CreatedAt,
                    Date = log.CreatedAt.ToString("dd MMMM yyyy"),
                    Time = log.CreatedAt.ToString("hh:mm tt")
                }).ToList();

                var paginationResult = new PaginationResult<CurrencyRuleAuditLogDto>(
                    auditLogDtos.ToArray(),
                    totalCount,
                    filter.Page,
                    filter.PageSize,
                    (int)Math.Ceiling((double)totalCount / filter.PageSize)
                );

                return new ApiResponse<PaginationResult<CurrencyRuleAuditLogDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Currency rule audit logs retrieved successfully",
                    Data = paginationResult
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<PaginationResult<CurrencyRuleAuditLogDto>>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<DecimalResponse>> GetConversionRate(string fromCurrency, string toCurrency, DateTime? effectiveDate = null, Applications? application = null)
        {
            try
            {
                if (!Enum.TryParse<CurrencyCode>(fromCurrency, out var fromCurrencyEnum) ||
                    !Enum.TryParse<CurrencyCode>(toCurrency, out var toCurrencyEnum))
                {
                    return new ApiResponse<DecimalResponse>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid currency code provided"
                    };
                }

                var query = _appDbContext.CurrencyConversionRates
                    .Where(x => x.FromCurrency == fromCurrencyEnum &&
                              x.ToCurrency == toCurrencyEnum &&
                              x.IsActive);

                if (application.HasValue)
                {
                    query = query.Where(x => x.Application == application.Value);
                }

                if (effectiveDate.HasValue)
                {
                    query = query.Where(x => x.EffectiveDate <= effectiveDate.Value &&
                                           (x.ExpiryDate == null || x.ExpiryDate >= effectiveDate.Value));
                }
                else
                {
                    query = query.Where(x => x.EffectiveDate <= DateTime.UtcNow &&
                                           (x.ExpiryDate == null || x.ExpiryDate >= DateTime.UtcNow));
                }

                var rate = await query
                    .OrderByDescending(x => x.EffectiveDate)
                    .FirstOrDefaultAsync();

                if (rate == null)
                {
                    return new ApiResponse<DecimalResponse>
                    {
                        ResponseCode = "404",
                        ResponseMessage = $"No conversion rate found for {fromCurrency} to {toCurrency}"
                    };
                }

                return new ApiResponse<DecimalResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Conversion rate retrieved successfully",
                    Data = new DecimalResponse(rate.ExchangeRate, $"{fromCurrency} to {toCurrency}")
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<DecimalResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<StringListResponse>> GetSupportedCurrencies()
        {
            try
            {
                var currencies = Enum.GetNames(typeof(CurrencyCode)).ToList();

                return new ApiResponse<StringListResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Supported currencies retrieved successfully",
                    Data = new StringListResponse(currencies)
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<StringListResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<StringListResponse>> GetBusinessLines()
        {
            try
            {
                var businessLines = new List<string> { "SaaS Seats", "Sophia Voice", "All" };

                return new ApiResponse<StringListResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Business lines retrieved successfully",
                    Data = new StringListResponse(businessLines)
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<StringListResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = $"An error occurred: {ex.Message}"
                };
            }
        }
    }
}
