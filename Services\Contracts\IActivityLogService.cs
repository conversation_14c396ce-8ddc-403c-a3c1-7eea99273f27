﻿using Configurations.Utility;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Dtos.ActivityLog;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IActivityLogService
    {
        Task LogActivity(ActivityActionType actionType, string oldValue, string description, string affectedObject, string performedByUserId, Applications? applications);
        Task<ApiResponse<PaginationResult<ActivityLogResponse>>> GetActivityLog(string userId, FilterActivity filterActivity);
    }
}
