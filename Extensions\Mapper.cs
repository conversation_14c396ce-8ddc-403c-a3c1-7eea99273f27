﻿using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Models.Dtos.AuthDomain;
using SuperAdmin.Service.Models.Dtos.SubscriptionDomain;

namespace SuperAdmin.Service.Extensions
{
    public static class Mapper
    {
        public static AuthenticatedUser ToAuthenticatedUser(this User user, string token, string? role, string refreshToken, string? roleId)
        {
            return new AuthenticatedUser
            {
                Email = user.Email!,
                FirstName = user.FirstName,
                LastName = user.LastName,
                PhoneNumber = user.PhoneNumber,
                RoleName = role,
                Token = token,
                UserId = user.Id,
                RefreshToken = refreshToken,
                RoleId = roleId
            };
        }

        public static EnterprizeCreationRequest ToEnterprizeCreationRequest(this EnterprizeCreationRequestDto request)
        {
            return new EnterprizeCreationRequest
            {
                CompanyName = request.CompanyName,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                CompanySize = request.CompanySize,
                Application = request.Application
            };
        }
    }
}
