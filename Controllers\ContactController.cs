using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Configurations.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos.ContactDomain;
using SuperAdmin.Service.Services.Contracts;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class ContactController : BaseController
    {
        private readonly IContactService _contactService;

        public ContactController(IContactService contactService)
        {
            _contactService = contactService;
        }

        /// <summary>
        /// Upload a single contact for the authenticated user
        /// </summary>
        /// <param name="model">Contact details</param>
        /// <returns>Created contact</returns>
        [HttpPost("upload-contact")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 400)]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 409)]
        public async Task<IActionResult> CreateContact([FromBody] CreateContactDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            model.Subdomain = Request.Headers["Subdomain"].FirstOrDefault();

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var response = await _contactService.CreateContact(userId, model);
            return ParseResponse(response);
        }

        /// <summary>
        /// Upload multiple contacts for the authenticated user
        /// </summary>
        /// <param name="contacts">List of contacts to upload</param>
        /// <returns>Upload summary</returns>
        [HttpPost("upload-contacts")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<ContactUploadResponseDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<ContactUploadResponseDto>), 400)]
        public async Task<IActionResult> UploadContacts([FromBody] List<CreateContactDto> contacts)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            // Get subdomain from headers
            contacts[0].Subdomain = Request.Headers["Subdomain"].FirstOrDefault();

            if (contacts == null || !contacts.Any())
            {
                return BadRequest("Contact list cannot be empty");
            }

            var response = await _contactService.UploadContacts(userId, contacts);
            return ParseResponse(response);
        }

        /// <summary>
        /// Get all contacts for the authenticated user
        /// </summary>
        /// <returns>List of user contacts</returns>
        [HttpGet("get-contacts")]
        [ProducesResponseType(typeof(ApiResponse<List<ContactDto>>), 200)]
        public async Task<IActionResult> GetContacts()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            // Get subdomain from headers
            var subdomain = Request.Headers["Subdomain"].FirstOrDefault();

            var response = await _contactService.GetUserContacts(userId, subdomain);
            return ParseResponse(response);
        }

        /// <summary>
        /// Check if the authenticated user has uploaded any contacts
        /// </summary>
        /// <returns>Boolean indicating if user has uploaded contacts</returns>
        [HttpGet("has-uploaded-contact")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        public async Task<IActionResult> HasUploadedContacts()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var subdomain = Request.Headers["Subdomain"].FirstOrDefault();
            var response = await _contactService.HasUserUploadedContacts(userId, subdomain);
            return Ok(response);
        }

        /// <summary>
        /// Update a specific contact
        /// </summary>
        /// <param name="contactId">Contact ID</param>
        /// <param name="model">Updated contact details</param>
        /// <returns>Updated contact</returns>
        [HttpPut("update-contact/{contactId}")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 409)]
        public async Task<IActionResult> UpdateContact([FromRoute] Guid contactId, [FromBody] CreateContactDto model)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var response = await _contactService.UpdateContact(userId, contactId, model);
            return ParseResponse(response);
        }

        /// <summary>
        /// Delete a specific contact
        /// </summary>
        /// <param name="contactId">Contact ID</param>
        /// <returns>Success message</returns>
        [HttpDelete("delete-contact/{contactId}")]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 200)]
        [ProducesResponseType(typeof(ApiResponse<dynamic>), 404)]
        public async Task<IActionResult> DeleteContact([FromRoute] Guid contactId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            var response = await _contactService.DeleteContact(userId, contactId);
            return ParseResponse(response);
        }

        [HttpGet("get-all-contacts-for-subdomain")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<List<ContactDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<ContactDto>>), 404)]
        public async Task<IActionResult> GetAllContactsForSubdomain([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var subdomain = Request.Headers["Subdomain"].FirstOrDefault();
            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest("Subdomain cannot be empty");
            }

            var response = await _contactService.GetAllContactsForSubdomain(subdomain, pageNumber, pageSize);
            return ParseResponse(response);
        }

        // Get contact by ID
        [HttpGet("get-contact-by-id/{contactId}")]
        [Authorize(AuthenticationSchemes = "monolith-schema")]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<ContactDto>), 404)]
        public async Task<IActionResult> GetContactById([FromRoute] Guid contactId)
        {
            if (contactId == Guid.Empty)
            {
                return BadRequest("Invalid contact ID");
            }

            var response = await _contactService.GetContactById(contactId);
            return ParseResponse(response);
        }
    }
}
