﻿using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.EnterpriseDomain;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IEnterpricePlanService
    {
        Task<ApiResponse<object>> CreateEnterpricePlan(EnterpriseDto model, string userId, string subdomain, string accessToken);
        Task<ApiResponse<Page<EnterpriceData>>> GetEnterpricePlan(PaginationParameters parameters, string userId, string? subdomain, string? searchKeyword);
        Task<ApiResponse<object>> EditEnterpricePlan(UpdateEnterprisePlanDto model, string userId, string subdomain);
        Task<ApiResponse<object>> DeleteEnterpricePlan(string tenantId, string userId, string enterPriseSubId,string subdomain);
        Task<ApiResponse<object>> CreateEnterpricePlanForExistingCompany(ExistingCompanyEnterprisePlanDto model, string userId, string subdomain);
    }
}
