using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.CurrencyRuleDomain
{
    /// <summary>
    /// Data transfer object representing a currency rule with all its properties
    /// </summary>
    public class CurrencyRuleDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the currency rule
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the application this currency rule applies to
        /// </summary>
        public string Application { get; set; }

        /// <summary>
        /// Gets or sets the business line this currency rule applies to
        /// </summary>
        public string BusinessLine { get; set; }

        /// <summary>
        /// Gets or sets the currency being sold/offered to customers
        /// </summary>
        public string SellCurrency { get; set; }

        /// <summary>
        /// Gets or sets the currency used for internal cost calculations
        /// </summary>
        public string CostCurrency { get; set; }
        /// <summary>
        /// Gets or sets the exchange rate for selling this currency to customers
        /// </summary>
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the internal cost amount for this currency conversion
        /// </summary>
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the type of external supplier used for this currency rule
        /// </summary>
        public string ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the cost charged by the external supplier for this currency conversion
        /// </summary>
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the date when this currency rule becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the current status of this currency rule (Active/Inactive)
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created this currency rule
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who last modified this currency rule
        /// </summary>
        public string ModifiedBy { get; set; }

        /// <summary>
        /// Gets or sets the date and time when this currency rule was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time when this currency rule was last modified
        /// </summary>
        public DateTime ModifiedAt { get; set; }

        /// <summary>
        /// Gets or sets the total number of versions for this currency rule
        /// </summary>
        public int VersionCount { get; set; }
    }

    /// <summary>
    /// Data transfer object for creating a new currency rule
    /// </summary>
    public class CreateCurrencyRuleDto
    {
        /// <summary>
        /// Gets or sets the application this currency rule applies to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// Gets or sets the business line this currency rule applies to
        /// </summary>
        public string BusinessLine { get; set; }

        /// <summary>
        /// Gets or sets the currency being sold/offered to customers
        /// </summary>
        public CurrencyCode SellCurrency { get; set; }

        /// <summary>
        /// Gets or sets the currency used for internal cost calculations
        /// </summary>
        public CurrencyCode CostCurrency { get; set; }
        /// <summary>
        /// Gets or sets the exchange rate for selling this currency to customers
        /// </summary>
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the internal cost amount for this currency conversion
        /// </summary>
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the type of external supplier used for this currency rule
        /// </summary>
        public ExternalSupplierType ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the cost charged by the external supplier
        /// </summary>
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the date when this currency rule becomes effective
        /// </summary>
        public DateTime EffectiveDate { get; set; }
    }

    /// <summary>
    /// Data transfer object for updating an existing currency rule
    /// </summary>
    public class UpdateCurrencyRuleDto
    {
        /// <summary>
        /// Gets or sets the new exchange rate for selling this currency to customers
        /// </summary>
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the new internal cost amount for this currency conversion
        /// </summary>
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the new type of external supplier used for this currency rule
        /// </summary>
        public ExternalSupplierType ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the new cost charged by the external supplier
        /// </summary>
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the new effective date for this currency rule
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the reason for updating this currency rule
        /// </summary>
        public string Reason { get; set; }
    }

    /// <summary>
    /// Data transfer object representing a currency rule version
    /// </summary>
    public class CurrencyRuleVersionDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for this version
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the application this version applies to
        /// </summary>
        public string Application { get; set; }

        /// <summary>
        /// Gets or sets the version number
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// Gets or sets the sell rate for this version
        /// </summary>
        public decimal SellRate { get; set; }

        /// <summary>
        /// Gets or sets the cost amount for this version
        /// </summary>
        public decimal CostAmount { get; set; }

        /// <summary>
        /// Gets or sets the external supplier type for this version
        /// </summary>
        public string ExternalSupplier { get; set; }

        /// <summary>
        /// Gets or sets the external supplier cost for this version
        /// </summary>
        public decimal ExternalSupplierCost { get; set; }

        /// <summary>
        /// Gets or sets the effective date for this version
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// Gets or sets the status of this version
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created this version
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the creation date of this version
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }

    public class CurrencyRuleFilterDto
    {
        public Applications? Application { get; set; }
        public string? BusinessLine { get; set; }
        public CurrencyCode? SellCurrency { get; set; }
        public CurrencyCode? CostCurrency { get; set; }
        public CurrencyRuleStatus? Status { get; set; }
        public DateTime? EffectiveDateFrom { get; set; }
        public DateTime? EffectiveDateTo { get; set; }
        public string? SearchKeyword { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
