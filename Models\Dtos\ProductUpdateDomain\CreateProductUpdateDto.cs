﻿using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.ProductUpdateDomain
{
    public class CreateProductUpdateDto
    {
        public Applications? Applications { get; set; }
        public JobSuitePackage? Package { get; set; }
        public Guid? CategoryId { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public IFormFile? ImageAttachment { get; set; }
    }

    public class DeleteProductUpdateDto
    {
        public Applications? Applications { get; set; }
        public List<Guid> Ids { get; set; }
    }
}
