﻿namespace SuperAdmin.Service.Database.Entities
{
    public class Company : BaseEntity
    {
        public Company() 
        { 
            Id = Guid.NewGuid();
            CreatedAt = DateTime.UtcNow;
            ModifiedAt = DateTime.UtcNow;
        }

        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Subdomain { get; set; }        
        public string CountryId { get; set; }
        public string AdminUserId { get; set; }

        public virtual EnterprisePlan EnterprisePlan { get; set; }
    }
}
