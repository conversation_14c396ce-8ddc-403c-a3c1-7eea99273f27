﻿using Configurations.Utility;
using Microsoft.AspNetCore.Identity;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Database.Enums;
using SuperAdmin.Service.Helpers;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Dtos.ActivityLog;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Globalization;

namespace SuperAdmin.Service.Services.Implementations
{
    public class ActivityLogService : IActivityLogService
    {
        private readonly AppDbContext _dbContext;
        private readonly UserManager<User> _userManager;

        public ActivityLogService(AppDbContext dbContext, UserManager<User> userManager)
        {
            _dbContext = dbContext;
            _userManager = userManager;
        }
      
        public async Task LogActivity(ActivityActionType actionType, string oldValue, string description, string affectedObject, string performedByUserId, Applications? applications)
        {
            var activityLog = new ActivityLog
            {
                ActionType = actionType,
                OldValue = oldValue,
                DescriptionOfActionPerformed = description,
                AffectedObject = affectedObject,
                PerformedByUserId = performedByUserId,
                Applications = applications
            };

            _dbContext.ActivityLogs.Add(activityLog);
            await _dbContext.SaveChangesAsync();
        }
        public async Task<ApiResponse<PaginationResult<ActivityLogResponse>>> GetActivityLog(string userId, FilterActivity filterActivity)
        {

            //var user = await _userManager.FindByIdAsync(userId);
            //if (user is null)
            //{
            //    return new ApiResponse<PaginationResult<ActivityLogResponse>>
            //    {
            //        ResponseCode = "404",
            //        ResponseMessage = "User not found"
            //    };
            //}
            var query = _dbContext.ActivityLogs.Where(x => !string.IsNullOrWhiteSpace(x.PerformedByUserId));

            if (string.IsNullOrWhiteSpace(filterActivity.sDate) && string.IsNullOrWhiteSpace(filterActivity.eDate) && string.IsNullOrWhiteSpace(filterActivity.SearchKeyword) && string.IsNullOrWhiteSpace(filterActivity.ActionType.ToString()))
            {
                DateTime currentDate = DateTime.Today;

                DateTime startDate = currentDate.AddDays(-(int)currentDate.DayOfWeek); // Get the first day of the current week (Sunday)
                DateTime endDate = startDate.AddDays(6); // Get the last day of the current week (Saturday)

                query.Where(x => x.CreatedAt.Date >= startDate && x.CreatedAt.Date <= endDate);
            }
            else if (!string.IsNullOrWhiteSpace(filterActivity.sDate) && !string.IsNullOrWhiteSpace(filterActivity.eDate))
            {
                DateTime parsedStartDate = DateTime.ParseExact(filterActivity.sDate.Trim(), "yyyy-MM-dd", CultureInfo.InvariantCulture);
                DateTime parsedEndDate = DateTime.ParseExact(filterActivity.eDate.Trim(), "yyyy-MM-dd", CultureInfo.InvariantCulture);

                query = query.Where(x => x.CreatedAt.Date >= parsedStartDate && x.CreatedAt.Date <= parsedEndDate);
            }

            query = ApplyApplicationType(query, filterActivity.Applications.ToString());
            query = ApplySearchKeyword(query, filterActivity.SearchKeyword);
            query = ApplyActionType(query, filterActivity.ActionType.ToString());

            // Paginate the query
            var paginatedData = await PaginationHelper.PaginateRecords(query, filterActivity.Page!.Value, filterActivity.PageSize!.Value);

            // Transform the paginated data in the memory
            var activityLogResponses = paginatedData.Items
                .GroupBy(x => x.CreatedAt.Date)
                .OrderByDescending(g => g.Key)
                .Select(group => new ActivityLogResponse
                {
                    Date = group.Key.ToString("dd MMMM yyyy"),
                    Activities = group.Select(x =>
                    {
                        var descriptionParts = x.DescriptionOfActionPerformed.Split(new[] { ' ' }, 2);
                        var user = descriptionParts.Length == 0 ? string.Empty: descriptionParts[0];
                        var actionDescription = descriptionParts.Length > 1 ? descriptionParts[1] : string.Empty;

                        return new ActivityLogDto
                        {
                            Date = x.CreatedAt.ToString("dd MMMM yyyy"),
                            Time = x.CreatedAt.ToString("hh:mm tt"),
                            ActionDescription = actionDescription,
                            User = user,
                            ActionType = x.ActionType switch
                            {
                                ActivityActionType.Login => "Login Notification",
                                ActivityActionType.Logout => "Logout Notification",
                                ActivityActionType.Assign => "Assign Notification",
                                ActivityActionType.Subscription => "Subscription Notification",
                                ActivityActionType.Deletion => "Delete Notification",
                                ActivityActionType.Creation => "Creation Notification",
                                ActivityActionType.Modification => "Modification Notification",
                                ActivityActionType.Publish => "Publish Notification",
                                _ => "View Notification",
                            },
                            AffectedObject = x.AffectedObject,
                        };
                    }).ToList()
                }).ToList();

            return new ApiResponse<PaginationResult<ActivityLogResponse>>
            {
                ResponseCode = "200",
                ResponseMessage = "successful",
                Data = new PaginationResult<ActivityLogResponse>(activityLogResponses.ToArray(), activityLogResponses.Count(), paginatedData.PageNumber, paginatedData.PageSize, paginatedData.TotalPages)
            };
        }

        private static IQueryable<ActivityLog> ApplySearchKeyword(IQueryable<ActivityLog> query, string? searchKeyword)
        {
            if (!string.IsNullOrEmpty(searchKeyword))
            {
                searchKeyword = searchKeyword.ToLower();

                query = query.Where(t =>
                    t.DescriptionOfActionPerformed.ToLower().Contains(searchKeyword));
            }
            return query;
        }

        private static IQueryable<ActivityLog> ApplyActionType(IQueryable<ActivityLog> query, string? actionType)
        {
            if (!string.IsNullOrEmpty(actionType) && Enum.TryParse(actionType, true, out ActivityActionType parsedActionType))
            {
                query = query.Where(t => t.ActionType == parsedActionType);
            }
            return query;
        }

        private static IQueryable<ActivityLog> ApplyApplicationType(IQueryable<ActivityLog> query, string? application)
        {
            if (!string.IsNullOrEmpty(application) && Enum.TryParse(application, true, out Applications parsedActionType))
            {
                query = query.Where(t => t.Applications == parsedActionType);
            }
            return query;
        }

    }



}
