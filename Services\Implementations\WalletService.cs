﻿
using Configurations.Utility;
using Serilog;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.WalletDomain;
using SuperAdmin.Service.Services.Contracts;
using System.Collections.Generic;
using System.Text.Json;
using ILogger = Serilog.ILogger;

namespace SuperAdmin.Service.Services.Implementations
{
    public class WalletService : IWalletService
    {
        private readonly ILogger _logger = Log.ForContext<WalletService>();
        private readonly HttpClient _httpClient;
        private readonly AppDbContext _dbContext;

        public WalletService(IConfiguration configuration, AppDbContext dbContext)
        {
            var baseAddress = configuration.GetSection("Services").GetSection("Monolith").Value;
            if (baseAddress == null)
                throw new ArgumentNullException(nameof(baseAddress), "The 'baseAddress' parameter cannot be null.");

           _httpClient = new HttpClient { BaseAddress = new Uri(baseAddress) };
            _dbContext = dbContext;
        }

        public Task<ApiResponse<PaginationResult<WalletSummaryResponse>>> GetWalletAnalysis()
        {
            throw new NotImplementedException();
        }

        public async Task<ApiResponse<Page<TransactionHistoryResponse>>> GetTransactionHistory(TransactionHistoryDto request, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/transaction/history?" +
                   $"PageNumber={request.Page}" +
                   $"&PageSize={request.PageSize}" +
                   $"&StartDate={request.FromDate}" +
                   $"&EndDate={request.ToDate}" +
                   $"&application={request.Application}" +
                   $"&providers={request.Provider}" +
                   $"&sortBy={request.SortBy}" +
                   $"&companyName={request.CompanyName}" +
                   $"&planId={request.PlanId}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<Page<TransactionHistoryResponse>>>(responseBody);

                    return new ApiResponse<Page<TransactionHistoryResponse>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<Page<TransactionHistoryResponse>>>(errorMessage)
                                           ?? new ApiResponse<Page<TransactionHistoryResponse>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetTransactionHistory: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetTransactionHistorys: {ex}");
                return new ApiResponse<Page<TransactionHistoryResponse>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }

        public async Task<ApiResponse<GetCorperateWalletBalanceResponse>> GetCorperateWalletBalance(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/corporate-wallet/history";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<GetCorperateWalletBalanceResponse>>(responseBody);

                    return new ApiResponse<GetCorperateWalletBalanceResponse>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<GetCorperateWalletBalanceResponse>>(errorMessage)
                                           ?? new ApiResponse<GetCorperateWalletBalanceResponse>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetCorperateWalletBalance: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetCorperateWalletBalance: {ex}");
                return new ApiResponse<GetCorperateWalletBalanceResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }



        public async Task<ApiResponse<List<GetAllWalletbalancesResponse>>> GetAllWalletbalances(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/transaction/GetAllWalletbalances";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<List<GetAllWalletbalancesResponse>>>(responseBody);

                    return new ApiResponse<List<GetAllWalletbalancesResponse>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();


                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<List<GetAllWalletbalancesResponse>>>(errorMessage)
                                           ?? new ApiResponse<List<GetAllWalletbalancesResponse>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetAllWalletbalances: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetAllWalletbalances: {ex}");
                return new ApiResponse<List<GetAllWalletbalancesResponse>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }




        public async Task<ApiResponse<Page<TransactionHistoryResponse>>> GetFailedPayments(TransactionHistoryDto request, string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/transaction/GetFailedPayments?" +
                                 $"application={request.Application}&" +
                                 $"pageNumber={request.Page}&pageSize={request.PageSize}";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<Page<TransactionHistoryResponse>>>(responseBody);

                    return new ApiResponse<Page<TransactionHistoryResponse>>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<Page<TransactionHistoryResponse>>>(errorMessage)
                                           ?? new ApiResponse<Page<TransactionHistoryResponse>>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetFailedPayments: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetFailedPayments: {ex}");
                return new ApiResponse<Page<TransactionHistoryResponse>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }




        public async Task<ApiResponse<GetTotalWalletBalanceResponse>> GetTotalWalletbalances(string? subdomain)
        {
            try
            {
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("subdomain", subdomain);

                var requestUri = $"/api/subscription/transaction/GetTotalWalletBalances";

                var response = await _httpClient.GetAsync(requestUri);

                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<ApiResponse<GetTotalWalletBalanceResponse>>(responseBody);

                    return new ApiResponse<GetTotalWalletBalanceResponse>
                    {
                        Data = result?.Data,
                        ResponseCode = result?.ResponseCode,
                        ResponseMessage = result?.ResponseMessage
                    };
                }
                else
                {
                    var statusCode = (int)response.StatusCode;
                    var errorMessage = await response.Content.ReadAsStringAsync();

                    var errorApiResponse = JsonSerializer.Deserialize<ApiResponse<GetTotalWalletBalanceResponse>>(errorMessage)
                                           ?? new ApiResponse<GetTotalWalletBalanceResponse>
                                           {
                                               ResponseCode = statusCode.ToString(),
                                               ResponseMessage = "Failed to fetch data"
                                           };

                    _logger.Error($"Error in GetTotalWalletBalances: {errorMessage}");

                    return errorApiResponse;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in GetTotalWalletBalances: {ex}");
                return new ApiResponse<GetTotalWalletBalanceResponse>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to fetch data"
                };
            }
        }


    }
}
