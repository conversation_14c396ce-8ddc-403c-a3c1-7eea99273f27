﻿using Common.Services.Utility;
using Configurations.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SuperAdmin.Service.Models.Dtos.ActivityDomain;
using SuperAdmin.Service.Models.Dtos.ActivityLog;
using SuperAdmin.Service.Models.Dtos.OtpDomain;
using SuperAdmin.Service.Models.Dtos.RuleEngineDomain;
using SuperAdmin.Service.Models.Enums;
using SuperAdmin.Service.Services.Contracts;
using System.Security.Claims;

namespace SuperAdmin.Service.Controllers
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class ActivityLogController : BaseController
    {
        private readonly IActivityLogService _activityService;

        public ActivityLogController(IActivityLogService activityService)
        {
            _activityService = activityService;
        }

        /// <summary>
        /// Get activity log 
        /// </summary>       
        /// <returns></returns>
        [HttpGet("get-activity-log")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(ApiResponse<List<ActivityLogResponse>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<ActivityLogResponse>>), 404)]       
        public async Task<IActionResult> GetActivityLog([FromQuery] FilterActivity filterActivity)
        {
           // string loggedInUser = User.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            var response = await _activityService.GetActivityLog("",filterActivity);
            return ParseResponse(response);
        }
    }
}
