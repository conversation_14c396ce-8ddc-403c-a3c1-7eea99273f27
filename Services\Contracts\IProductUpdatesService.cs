﻿using Configurations.Utility;
using SuperAdmin.Service.Models.Dtos;
using SuperAdmin.Service.Models.Dtos.ProductUpdateDomain;
using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Services.Contracts
{
    public interface IProductUpdatesService
    {
        ApiResponse<IEnumerable<string>> GetPackages();
        Task<ApiResponse<dynamic>> CreateProductUpdateCategory(string category, string userId, Applications? applications);
        Task<ApiResponse<IEnumerable<ProductUpdateCategoryDto>>> GetProductUpdateCategories();
        Task<ApiResponse<dynamic>> CreateProductUpdate(PublishCommand publishCommand, CreateProductUpdateDto model, string userId);
        Task<ApiResponse<PaginationResult<SentProductUpdateDto>>> GetSentProductUpdates(FilterProductUpdatesBy filterBy, string userId);
        Task<ApiResponse<PaginationResult<SentProductUpdateDto>>> GetSentProductUpdatesForAdmin(FilterProductUpdatesBy filterBy);
        Task<ApiResponse<PaginationResult<DraftProductUpdateDto>>> GetDraftProductUpdates(FilterProductUpdatesBy filterBy, string userId);
        Task<ApiResponse<PaginationResult<DeletedProductUpdateDto>>> GetDeletedProductUpdates(FilterProductUpdatesBy filterBy, string userId);
        Task<ApiResponse<dynamic>> UpdateDraftProductUpdates(Guid id, CreateProductUpdateDto model, string userId);
        Task<ApiResponse<dynamic>> SendDraftProductUpdates(Guid id, CreateProductUpdateDto model, string userId);
        Task<ApiResponse<dynamic>> DeleteProductUpdates(List<Guid> ids, string userId,Applications? applications);
        Task<ApiResponse<dynamic>> DeleteProductUpdatesForAdmin(List<Guid> ids);
    }
}
