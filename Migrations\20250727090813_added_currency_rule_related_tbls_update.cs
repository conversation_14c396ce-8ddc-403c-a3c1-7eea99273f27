﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SuperAdmin.Service.Migrations
{
    /// <inheritdoc />
    public partial class added_currency_rule_related_tbls_update : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CurrencyConversionRates_AspNetUsers_ModifiedByUserId",
                table: "CurrencyConversionRates");

            migrationBuilder.DropForeignKey(
                name: "FK_CurrencyRules_AspNetUsers_ModifiedByUserId",
                table: "CurrencyRules");

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedByUserId",
                table: "CurrencyRules",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedByUserId",
                table: "CurrencyConversionRates",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddForeignKey(
                name: "FK_CurrencyConversionRates_AspNetUsers_ModifiedByUserId",
                table: "CurrencyConversionRates",
                column: "ModifiedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CurrencyRules_AspNetUsers_ModifiedByUserId",
                table: "CurrencyRules",
                column: "ModifiedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CurrencyConversionRates_AspNetUsers_ModifiedByUserId",
                table: "CurrencyConversionRates");

            migrationBuilder.DropForeignKey(
                name: "FK_CurrencyRules_AspNetUsers_ModifiedByUserId",
                table: "CurrencyRules");

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedByUserId",
                table: "CurrencyRules",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ModifiedByUserId",
                table: "CurrencyConversionRates",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CurrencyConversionRates_AspNetUsers_ModifiedByUserId",
                table: "CurrencyConversionRates",
                column: "ModifiedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CurrencyRules_AspNetUsers_ModifiedByUserId",
                table: "CurrencyRules",
                column: "ModifiedByUserId",
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
