﻿using SuperAdmin.Service.Models.Enums;
using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Database.Entities
{
    public class EnterprizeCreationRequest
    {
        [Key]
        public Guid Id { get; set; }
        public string CompanyName { get; set; } = default!;
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string Email { get; set; } = default!;
        public string PhoneNumber { get; set; } = default!;
        public string CompanySize { get; set; }
        public Applications Application { get; set; }
        public DateTime CreatedOn { get; set; }

        public EnterprizeCreationRequest()
        {
            Id = Guid.NewGuid();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
