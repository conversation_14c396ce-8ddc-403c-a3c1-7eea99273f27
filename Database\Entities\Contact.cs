using System.ComponentModel.DataAnnotations;

namespace SuperAdmin.Service.Database.Entities
{
    public class Contact : BaseEntity
    {
        public Contact()
        {
            Id = Guid.NewGuid();
            Name = string.Empty;
            PhoneNumber = string.Empty;
            Email = null;
            UserId = string.Empty;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string? Email { get; set; }

        [Required]
        [Phone]
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        [Required]
        [StringLength(50)]
        public string Subdomain { get; set; }

        public bool IsDeleted { get; set; } = false;

        public string? Industry { get; set; }
    }
}
