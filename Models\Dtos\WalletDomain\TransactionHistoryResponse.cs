﻿using SuperAdmin.Service.Models.Enums;

namespace SuperAdmin.Service.Models.Dtos.WalletDomain
{
    public class TransactionHistoryResponse
    {
        public string CompanyName { get; set; }
        public DateTime? TransactionDate { get; set; }
        public double Amount { get; set; }
        public string Package { get; set; }
        public string TransactionType { get; set; }
        public string? PaymentProviders { get; set; }
        public string? Plans { get; set; }
    }

    public class GetAllWalletbalancesResponse
    {
        public double Amount { get; set; }
        public string? Package { get; set; }
    }

}
