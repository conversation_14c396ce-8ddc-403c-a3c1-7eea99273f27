using Configurations;
using Configurations.Extensions.Middlewares;
using Configurations.Extensions.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Converters;
using SuperAdmin.Service.Database;
using SuperAdmin.Service.Database.Entities;
using SuperAdmin.Service.Extensions;
using SuperAdmin.Service.Hubs;
using SuperAdmin.Service.Mappings;
using SuperAdmin.Service.Services.GrpcServices.Server;

var builder = WebApplication.CreateBuilder(args);
var hostEnvironment = builder.Environment;

builder.Services.AddEndpointsApiExplorer();

// Register context
builder.Services.AddDbContext<AppDbContext>(options =>
{
    options.UseNpgsql(DatabaseConnectionString.ConnectionString);
});

builder.Services.AddHttpClient();
builder.Services.ConfigureAppSettings(builder.Configuration);
builder.Services.AddAuthentication(builder.Configuration);
builder.Services.InjectServices(builder.Configuration, hostEnvironment);
builder.Services.InjectSuperAdminServices();

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(CurrencyRuleMappingProfile));

builder.Services.AddGrpc();
builder.Services.AddControllers()
        .AddNewtonsoftJson(options => options.SerializerSettings.Converters.Add(new StringEnumConverter()));
builder.Services.AddSwaggerGenNewtonsoftSupport();

// Add SwaggerCustomerHeaderConfigs
builder.Services.AddSwaggerGen(c =>
{
    c.OperationFilter<SwaggerCustomerHeaderConfigs>();
});


builder.Services.AddCors(options =>
{
    options.AddPolicy(name: "JobProPolicy",
                      policy =>
                      {
                          policy.WithOrigins("*");
                          policy.AllowAnyOrigin();
                          policy.AllowAnyHeader();
                          policy.AllowAnyMethod();
                      });
});
builder.Services.AddSignalR();

IServiceProvider serviceProvider = builder.Services.BuildServiceProvider();
var context = serviceProvider.GetRequiredService<AppDbContext>();

// Apply migration
context.Database.Migrate();

var userManager = serviceProvider.GetRequiredService<UserManager<User>>();
await DataSeeder.Seed(context, userManager);

var app = builder.Build();

// Map Grpc Service
app.MapGrpcService<SalaryDisbursementService>();

// Configure the HTTP request pipeline.
app.UseRouting();
app.UseCors("JobProPolicy");

app.InjectMiddlewares();
app.UseAuthentication();
app.UseTokenRevocationMiddleware();

app.UseAuthorization();
app.MapControllers();
app.MapHub<TicketHub>("/ticketHub").RequireAuthorization();

app.Run();
