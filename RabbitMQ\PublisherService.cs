﻿
using RabbitMQ.Client;
using Serilog;
using ILogger = Serilog.ILogger;
using System.Text;
using System.Text.Json;

namespace SuperAdmin.Service.RabbitMQ
{
    public class PublisherService : IPublisherService
    {
        #region Properties & Constructors
        private readonly IConnection? _connection;
        private readonly ILogger _logger = Log.ForContext<PublisherService>();
        private readonly IModel _channel;

        public PublisherService(IConfiguration config)
        {
            _connection = GetRabbitMQConnection(config);
            if (_connection is not null)
            {
                _channel = _connection.CreateModel();
            }
        }

        #endregion
        public async Task<bool> GenericPublish(PublishRecord record)
        {
            if (_channel is null)
            {
                _logger.Error("RabbitMQ channel is null");
                return false;
            }

            _channel.ExchangeDeclare(exchange: record.ExchangeName, type: record.ExchangeType);

            foreach (var queue in record.QueueNames)
            {
                _channel.QueueDeclare(queue: queue, durable: true, exclusive: false, autoDelete: false, arguments: null);
                _channel.QueueBind(queue: queue, exchange: record.ExchangeName, routingKey: record.RoutingKey);
            }

            var message = JsonSerializer.Serialize(record.Message);

            if (_connection is not null)
            {
                if (_connection.IsOpen)
                {
                    var isSuccessful = await SendMessage(message, record.ExchangeName, record.RoutingKey);
                    return isSuccessful;
                }
                else
                {
                    _connection?.Close();
                    _connection?.Dispose();
                }
            }

            return false;
        }

        private IConnection? GetRabbitMQConnection(IConfiguration config)
        {
            ConnectionFactory connectionFactory = new ConnectionFactory
            {
                HostName = config["RabbitMQConfiguration:Host"],
                Port = Convert.ToInt32(config["RabbitMQConfiguration:Port"]),
                UserName = config["RabbitMQConfiguration:Username"],
                Password = config["RabbitMQConfiguration:Password"],
            };

            try
            {
                return connectionFactory.CreateConnection();
            }
            catch (Exception ex)
            {
                _logger.Error("RabbitMQ connection failed - " + ex.Message);
                return null;
            }
        }

        private async Task<bool> SendMessage(string message, string exchangeName, string routingKey)
        {
            var body = Encoding.UTF8.GetBytes(message);

            _channel.BasicPublish(exchange: exchangeName, routingKey: routingKey, body: body);
            return await Task.FromResult(true);
        }
    }
}
