﻿using Serilog.Events;
using Twilio.TwiML.Voice;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace SuperAdmin.Service.Models.Dtos.SubscriptionDomain
{
    public class ApplicationSummary
    {
        public object user { get; set; }
        public ApplicationData Data { get; set; }
        public string ResponseCode { get; set; }
        public object ErrorCode { get; set; }
        public string ResponseMessage { get; set; }
        public object DevResponseMessage { get; set; }
        public object StackTrace { get; set; }
    }

    public class ApplicationData
    {
        public ApplicationStatistic Joble { get; set; }
        public ApplicationStatistic Echo { get; set; }
        public ApplicationStatistic JobPays { get; set; }
        public ApplicationStatistic JobID { get; set; }
        public ApplicationStatistic JobEvent { get; set; }
        public ApplicationStatistic JobEyes { get; set; }
        public ApplicationStatistic CaringBoss { get; set; }
    }
}
